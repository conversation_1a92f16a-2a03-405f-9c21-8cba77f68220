{"version": 3, "file": "types-ffi.js", "sourceRoot": "", "sources": ["../ts/types-ffi.ts"], "names": [], "mappings": ";;;AAyGA,SAAgB,UAAU,CAAwB,EAAwB;IACxE,OAAO,SAAS,UAAU,CAAC,GAAG,IAAU;QACtC,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;QAC1B,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,YAAY,OAAO,EAAE;YACrE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAA;SAC5D;QACD,OAAO,MAAM,CAAA;IACf,CAAC,CAAA;AACH,CAAC;AARD,gCAQC;AAED,iDAAiD;AACpC,QAAA,SAAS,GAAG;IACvB,4BAA4B;IAC5B,mBAAmB,EAAE,CAAC,IAAI,CAAC;IAC3B,kBAAkB;IAClB,mBAAmB,EAAE,CAAC,IAAI,CAAC;IAC3B,iCAAiC;IACjC,mBAAmB,EAAE,CAAC,IAAI,CAAC;IAC3B,mCAAmC;IACnC,qBAAqB,EAAE,CAAC,IAAI,CAAC;IAC7B,iBAAiB,EAAE,CAAC,IAAI,CAAC;IACzB,0BAA0B;IAC1B,mBAAmB,EAAE,CAAC,IAAI,CAAC;IAC3B,yBAAyB;IACzB,kBAAkB,EAAE,CAAC,IAAI,CAAC;IAC1B;;;;OAIG;IACH,yBAAyB,EAAE,CAAC,IAAI,CAAC;IACjC,gFAAgF;IAChF,8BAA8B,EAAE,CAAC,IAAI,CAAC;CACvC,CAAA", "sourcesContent": ["/**\n * C pointer to type `CType`. Pointer types are used internally for FFI, but\n * are not intended for external use.\n *\n * @unstable This type is considered private and may change.\n */\ntype Pointer<CType extends string> = number & { ctype: CType }\n\ntype Brand<T, B> = T & { brand: B }\n\n/**\n * `JSRuntime*`.\n */\nexport type JSRuntimePointer = Pointer<\"JSRuntime\">\n\n/**\n * `JSContext*`.\n */\nexport type JSContextPointer = Pointer<\"JSContext\">\n\n/**\n * `JSContext**`. Used internally for execute pending jobs.\n */\nexport type JSContextPointerPointer = Pointer<\"JSContext\">\n\n/**\n * `JSModuleDef*`.\n */\nexport type JSModuleDefPointer = Pointer<\"JSModuleDef\">\n\n/**\n * `JSValue*`.\n * See [[JSValue]].\n */\nexport type JSValuePointer = Pointer<\"JSValue\">\n\n/**\n * `JSValueConst*\n * See [[JSValueConst]] and [[StaticJSValue]].\n */\nexport type JSValueConstPointer = Pointer<\"JSValueConst\">\n\n/**\n * Used internally for Javascript-to-C function calls.\n */\nexport type JSValuePointerPointer = Pointer<\"JSValue[]\">\n\n/**\n * Used internally for Javascript-to-C function calls.\n */\nexport type JSValueConstPointerPointer = Pointer<\"JSValueConst[]\">\n\n/**\n * Used internally for C-to-Javascript function calls.\n */\n// type JSCFunctionPointer = Pointer<'JSCFunction'>\n\n/**\n * Used internally for C-to-Javascript function calls.\n */\nexport type QTS_C_To_HostCallbackFuncPointer = Pointer<\"C_To_HostCallbackFunc\">\n\n/**\n * Used internally for C-to-Javascript interrupt handlers.\n */\nexport type QTS_C_To_HostInterruptFuncPointer = Pointer<\"C_To_HostInterruptFunc\">\n\n/**\n * Used internally for C-to-Javascript module loading.\n */\nexport type QTS_C_To_HostLoadModuleFuncPointer = Pointer<\"C_To_HostLoadModuleFunc\">\n\n/**\n * Used internally for Javascript-to-C calls that may contain strings too large\n * for the Emscripten stack.\n */\nexport type BorrowedHeapCharPointer = Pointer<\"const char\" | \"char\" | \"js const char\">\n\n/**\n * Used internally for Javascript-to-C calls that may contain strings too large\n * for the Emscripten stack.\n */\nexport type OwnedHeapCharPointer = Pointer<\"char\">\n\n/**\n * Used internally for Javascript-to-C calls that may contain strings too large\n * for the Emscripten stack.\n */\nexport type JSBorrowedCharPointer = Pointer<\"js const char\">\n\n/**\n * Opaque pointer that was allocated by js_malloc.\n */\nexport type JSVoidPointer = Pointer<any>\n\n/**\n * @private\n */\nexport type EvalFlags = Brand<number, \"EvalFlags\">\n\n/**\n * @private\n */\nexport type EvalDetectModule = Brand<number, \"EvalDetectModule\">\n\nexport function assertSync<Args extends any[], R>(fn: (...args: Args) => R): (...args: Args) => R {\n  return function mustBeSync(...args: Args): R {\n    const result = fn(...args)\n    if (result && typeof result === \"object\" && result instanceof Promise) {\n      throw new Error(\"Function unexpectedly returned a Promise\")\n    }\n    return result\n  }\n}\n\n/** Bitfield options for JS_Eval() C function. */\nexport const EvalFlags = {\n  /** global code (default) */\n  JS_EVAL_TYPE_GLOBAL: 0 << 0,\n  /** module code */\n  JS_EVAL_TYPE_MODULE: 1 << 0,\n  /** direct call (internal use) */\n  JS_EVAL_TYPE_DIRECT: 2 << 0,\n  /** indirect call (internal use) */\n  JS_EVAL_TYPE_INDIRECT: 3 << 0,\n  JS_EVAL_TYPE_MASK: 3 << 0,\n  /** force 'strict' mode */\n  JS_EVAL_FLAG_STRICT: 1 << 3,\n  /** force 'strip' mode */\n  JS_EVAL_FLAG_STRIP: 1 << 4,\n  /**\n   * compile but do not run. The result is an object with a\n   * JS_TAG_FUNCTION_BYTECODE or JS_TAG_MODULE tag. It can be executed\n   * with JS_EvalFunction().\n   */\n  JS_EVAL_FLAG_COMPILE_ONLY: 1 << 5,\n  /** don't include the stack frames before this eval in the Error() backtraces */\n  JS_EVAL_FLAG_BACKTRACE_BARRIER: 1 << 6,\n}\n"]}