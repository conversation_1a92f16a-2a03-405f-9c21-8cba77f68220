import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token automaticamente nas requisições
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('axiom-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para tratar respostas e erros
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Só redirecionar para login se for 401 E não for uma tentativa de login
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/login')) {
      // Token inválido ou expirado - mas não durante o login
      localStorage.removeItem('axiom-token');
      localStorage.removeItem('axiom-user');

      // Só redirecionar se não estivermos já na página de login
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default api;
