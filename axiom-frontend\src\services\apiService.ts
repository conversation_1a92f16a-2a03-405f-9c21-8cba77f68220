import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token automaticamente nas requisições
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('axiom-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// INTERCEPTOR DESABILITADO TEMPORARIAMENTE PARA DEBUG
// api.interceptors.response.use(
//   (response) => {
//     return response;
//   },
//   (error) => {
//     // NÃO FAZER LOGOUT AUTOMÁTICO POR ENQUANTO
//     return Promise.reject(error);
//   }
// );

export default api;
