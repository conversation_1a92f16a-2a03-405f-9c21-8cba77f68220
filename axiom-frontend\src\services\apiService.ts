import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token automaticamente nas requisições
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('axiom-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para tratar respostas e erros
api.interceptors.response.use(
  (response) => {
    console.log('[API] Resposta bem-sucedida:', response.config.url);
    return response;
  },
  (error) => {
    console.log('[API] Erro na requisição:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.response?.data?.message
    });

    // Só fazer logout automático em casos muito específicos
    if (error.response?.status === 401) {
      const url = error.config?.url || '';
      const isLoginRequest = url.includes('/auth/login') || url.includes('/auth/register');
      const isCurrentlyOnLoginPage = window.location.pathname === '/login';

      console.log('[API] Erro 401 detectado:', {
        url,
        isLoginRequest,
        isCurrentlyOnLoginPage,
        willLogout: !isLoginRequest && !isCurrentlyOnLoginPage
      });

      // Só fazer logout se:
      // 1. NÃO for uma tentativa de login/registro
      // 2. NÃO estivermos já na página de login
      // 3. FOR uma requisição autenticada que falhou
      if (!isLoginRequest && !isCurrentlyOnLoginPage) {
        console.log('[API] Fazendo logout automático devido a token inválido');
        localStorage.removeItem('axiom-token');
        localStorage.removeItem('axiom-user');

        // Usar setTimeout para evitar conflitos com navegação
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      }
    }
    return Promise.reject(error);
  }
);

export default api;
