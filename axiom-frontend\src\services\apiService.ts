import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token automaticamente nas requisições
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('axiom-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor reativado de forma segura
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Só fazer logout em casos muito específicos
    if (error.response?.status === 401) {
      const url = error.config?.url || '';
      const isAuthRequest = url.includes('/auth/');
      const isOnLoginPage = window.location.pathname === '/login';

      // NUNCA fazer logout se:
      // 1. For uma requisição de autenticação (login/register)
      // 2. Já estivermos na página de login
      if (!isAuthRequest && !isOnLoginPage) {
        console.warn('[API] Token expirado, fazendo logout');
        localStorage.removeItem('axiom-token');
        localStorage.removeItem('axiom-user');

        // Redirecionar após um pequeno delay
        setTimeout(() => {
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }, 500);
      }
    }
    return Promise.reject(error);
  }
);

export default api;
