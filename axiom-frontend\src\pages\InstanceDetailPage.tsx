import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  RefreshCw, 
  QrCode, 
  CheckCircle, 
  Clock,
  AlertCircle,
  Copy,
  ExternalLink
} from 'lucide-react';
import api from '../services/apiService';

interface Instance {
  id: string;
  name: string;
  instanceId: string;
  status: 'CREATED' | 'CONNECTED' | 'DISCONNECTED';
  qrCode?: string;
  webhookUrl?: string;
  createdAt: string;
  updatedAt: string;
}

const InstanceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [instance, setInstance] = useState<Instance | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'instance' | 'webhooks'>('instance');
  const [refreshingQR, setRefreshingQR] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('');

  useEffect(() => {
    if (id) {
      fetchInstanceDetails();
    }
  }, [id]);

  const fetchInstanceDetails = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/instances/${id}`);
      const instanceData = response.data.instance;
      setInstance(instanceData);
      setWebhookUrl(instanceData.webhookUrl || '');
    } catch (error: any) {
      console.error('Erro ao buscar detalhes da instância:', error);
      if (error.response?.status === 404) {
        alert('Instância não encontrada');
        navigate('/instances');
      } else {
        alert('Erro ao carregar detalhes da instância');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchQRCode = async () => {
    try {
      setRefreshingQR(true);
      const response = await api.get(`/instances/${id}/qr`);
      if (response.data.qrCode) {
        setInstance(prev => prev ? { ...prev, qrCode: response.data.qrCode } : null);
      }
    } catch (error: any) {
      console.error('Erro ao buscar QR Code:', error);
      if (error.response?.data?.message) {
        alert(`Erro: ${error.response.data.message}`);
      }
    } finally {
      setRefreshingQR(false);
    }
  };

  const checkStatus = async () => {
    try {
      const response = await api.get(`/instances/${id}/status`);
      setInstance(prev => prev ? { ...prev, status: response.data.status } : null);
    } catch (error) {
      console.error('Erro ao verificar status:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copiado para a área de transferência!');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'CREATED':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'DISCONNECTED':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'Conectada';
      case 'CREATED':
        return 'Aguardando QR Code';
      case 'DISCONNECTED':
        return 'Desconectada';
      default:
        return 'Desconhecido';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'text-green-400 bg-green-500/20';
      case 'CREATED':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'DISCONNECTED':
        return 'text-red-400 bg-red-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!instance) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Instância não encontrada</h1>
          <button
            onClick={() => navigate('/instances')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Voltar para Instâncias
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/instances')}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-white">{instance.name}</h1>
            <p className="text-gray-400">ID: {instance.instanceId}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(instance.status)}`}>
            {getStatusIcon(instance.status)}
            <span className="ml-2">{getStatusText(instance.status)}</span>
          </div>
          <button
            onClick={checkStatus}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('instance')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'instance'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Dados da Instância
          </button>
          <button
            onClick={() => setActiveTab('webhooks')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'webhooks'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Webhooks
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'instance' && (
        <div className="space-y-6">
          {/* Instance Info */}
          <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
            <h2 className="text-xl font-bold text-white mb-4">Informações da Instância</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Nome</label>
                <div className="flex items-center space-x-2">
                  <span className="text-white">{instance.name}</span>
                  <button
                    onClick={() => copyToClipboard(instance.name)}
                    className="p-1 text-gray-400 hover:text-white transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">ID da Instância</label>
                <div className="flex items-center space-x-2">
                  <span className="text-white font-mono text-sm">{instance.instanceId}</span>
                  <button
                    onClick={() => copyToClipboard(instance.instanceId)}
                    className="p-1 text-gray-400 hover:text-white transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(instance.status)}
                  <span className="text-white">{getStatusText(instance.status)}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Criado em</label>
                <span className="text-white">
                  {new Date(instance.createdAt).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </div>
          </div>

          {/* QR Code Section */}
          <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">QR Code</h2>
              {(instance.status === 'CREATED' || instance.status === 'DISCONNECTED') && (
                <button
                  onClick={fetchQRCode}
                  disabled={refreshingQR}
                  className="flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${refreshingQR ? 'animate-spin' : ''}`} />
                  {refreshingQR ? 'Atualizando...' : 'Atualizar QR'}
                </button>
              )}
            </div>

            {instance.status === 'CONNECTED' ? (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Instância Conectada!</h3>
                <p className="text-gray-400">
                  Sua instância está conectada e pronta para enviar mensagens.
                </p>
              </div>
            ) : instance.qrCode ? (
              <div className="text-center">
                <div className="inline-block p-4 bg-white rounded-lg mb-4">
                  <img
                    src={instance.qrCode}
                    alt="QR Code"
                    className="w-64 h-64 mx-auto"
                  />
                </div>
                <div className="space-y-2">
                  <p className="text-white font-medium">Escaneie o QR Code com seu WhatsApp</p>
                  <p className="text-gray-400 text-sm">
                    1. Abra o WhatsApp no seu celular<br/>
                    2. Toque em Mais opções → Dispositivos conectados<br/>
                    3. Toque em Conectar um dispositivo<br/>
                    4. Aponte seu celular para esta tela para escanear o código
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <QrCode className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">QR Code não disponível</h3>
                <p className="text-gray-400 mb-4">
                  O QR Code ainda não foi gerado para esta instância.
                </p>
                <button
                  onClick={fetchQRCode}
                  disabled={refreshingQR}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {refreshingQR ? 'Gerando...' : 'Gerar QR Code'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'webhooks' && (
        <div className="space-y-6">
          <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
            <h2 className="text-xl font-bold text-white mb-4">Configuração de Webhooks</h2>
            <p className="text-gray-400 mb-6">
              Configure URLs para receber notificações em tempo real sobre mensagens e eventos.
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  URL do Webhook
                </label>
                <div className="flex space-x-2">
                  <input
                    type="url"
                    value={webhookUrl}
                    onChange={(e) => setWebhookUrl(e.target.value)}
                    placeholder="https://seu-site.com/webhook"
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={() => alert('Funcionalidade em desenvolvimento!')}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    Salvar
                  </button>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Esta URL receberá notificações POST sobre mensagens recebidas e eventos da instância.
                </p>
              </div>

              <div className="border-t border-gray-700 pt-4">
                <h3 className="text-lg font-medium text-white mb-3">Eventos Disponíveis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {[
                    'Mensagem recebida',
                    'Mensagem enviada',
                    'Status da instância',
                    'Conexão estabelecida',
                    'Conexão perdida',
                    'QR Code atualizado'
                  ].map((event, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`event-${index}`}
                        defaultChecked
                        className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                      />
                      <label htmlFor={`event-${index}`} className="text-sm text-gray-300">
                        {event}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border-t border-gray-700 pt-4">
                <h3 className="text-lg font-medium text-white mb-3">Documentação</h3>
                <p className="text-gray-400 text-sm mb-3">
                  Consulte nossa documentação para implementar corretamente os webhooks.
                </p>
                <a
                  href="https://docs.axiom-api.com/webhooks"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Ver Documentação
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstanceDetailPage;
