import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Key, 
  Copy, 
  RefreshCw, 
  Eye, 
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Lock
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/apiService';

interface ApiToken {
  token: string;
  createdAt: string;
  lastUsed?: string;
}

const SecurityPage = () => {
  const { user } = useAuth();
  const [apiToken, setApiToken] = useState<ApiToken | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [showToken, setShowToken] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);

  useEffect(() => {
    fetchApiToken();
  }, []);

  const fetchApiToken = async () => {
    try {
      setLoading(true);
      // Por enquanto, simular dados do token
      // Em produção, seria: const response = await api.get('/auth/api-token');
      
      // Simulação de token existente
      const mockToken = {
        token: 'axm_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        createdAt: new Date().toISOString(),
        lastUsed: new Date(Date.now() - 86400000).toISOString() // 1 dia atrás
      };
      
      setApiToken(mockToken);
    } catch (error) {
      console.error('Erro ao buscar token da API:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateNewToken = async () => {
    if (!confirm('Tem certeza que deseja gerar um novo token? O token atual será invalidado.')) {
      return;
    }

    try {
      setGenerating(true);
      // Em produção: const response = await api.post('/auth/api-token/regenerate');
      
      // Simulação
      const newToken = {
        token: 'axm_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        createdAt: new Date().toISOString(),
        lastUsed: undefined
      };
      
      setApiToken(newToken);
      alert('Novo token gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar novo token:', error);
      alert('Erro ao gerar novo token');
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Token copiado para a área de transferência!');
  };

  const handlePasswordChange = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      alert('Preencha todos os campos');
      return;
    }

    if (newPassword !== confirmPassword) {
      alert('As senhas não coincidem');
      return;
    }

    if (newPassword.length < 8) {
      alert('A nova senha deve ter pelo menos 8 caracteres');
      return;
    }

    try {
      setChangingPassword(true);
      // Em produção: await api.post('/auth/change-password', { currentPassword, newPassword });
      
      // Simulação
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      alert('Senha alterada com sucesso!');
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      alert('Erro ao alterar senha');
    } finally {
      setChangingPassword(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const maskToken = (token: string) => {
    if (token.length <= 8) return token;
    return token.substring(0, 8) + '•'.repeat(token.length - 12) + token.substring(token.length - 4);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Segurança</h1>
        <p className="text-gray-400">
          Gerencie suas configurações de segurança e tokens de API
        </p>
      </div>

      <div className="space-y-6">
        {/* API Token Section */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Key className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Token de Segurança da Conta</h2>
                <p className="text-gray-400 text-sm">Use este token para autenticar suas requisições à API</p>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="animate-pulse">
              <div className="h-12 bg-gray-700 rounded mb-4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/3"></div>
            </div>
          ) : apiToken ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Token da API
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={showToken ? apiToken.token : maskToken(apiToken.token)}
                      readOnly
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white font-mono text-sm focus:outline-none"
                    />
                    <button
                      onClick={() => setShowToken(!showToken)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                  <button
                    onClick={() => copyToClipboard(apiToken.token)}
                    className="p-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={generateNewToken}
                    disabled={generating}
                    className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    <RefreshCw className={`w-4 h-4 mr-2 ${generating ? 'animate-spin' : ''}`} />
                    {generating ? 'Gerando...' : 'Gerar Novo'}
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Criado em:</span>
                  <span className="text-white ml-2">{formatDate(apiToken.createdAt)}</span>
                </div>
                {apiToken.lastUsed && (
                  <div>
                    <span className="text-gray-400">Último uso:</span>
                    <span className="text-white ml-2">{formatDate(apiToken.lastUsed)}</span>
                  </div>
                )}
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-yellow-400 font-medium mb-1">Importante</h3>
                    <p className="text-yellow-200 text-sm">
                      Mantenha seu token seguro e não o compartilhe. Se você suspeitar que foi comprometido, 
                      gere um novo token imediatamente.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Key className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">Nenhum token encontrado</h3>
              <p className="text-gray-400 mb-4">Gere seu primeiro token de API para começar</p>
              <button
                onClick={generateNewToken}
                disabled={generating}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {generating ? 'Gerando...' : 'Gerar Token'}
              </button>
            </div>
          )}
        </div>

        {/* Password Change Section */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <Lock className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Alterar Senha</h2>
              <p className="text-gray-400 text-sm">Mantenha sua conta segura com uma senha forte</p>
            </div>
          </div>

          <div className="space-y-4 max-w-md">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Senha Atual
              </label>
              <input
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Digite sua senha atual"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nova Senha
              </label>
              <input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Digite sua nova senha"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Confirmar Nova Senha
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Confirme sua nova senha"
              />
            </div>

            <button
              onClick={handlePasswordChange}
              disabled={changingPassword || !currentPassword || !newPassword || !confirmPassword}
              className="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {changingPassword ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Alterando...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Alterar Senha
                </>
              )}
            </button>
          </div>
        </div>

        {/* Account Info */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Shield className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Informações da Conta</h2>
              <p className="text-gray-400 text-sm">Detalhes da sua conta e permissões</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Nome</label>
              <span className="text-white">{user?.name}</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">E-mail</label>
              <span className="text-white">{user?.email}</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Tipo de Conta</label>
              <span className="text-white capitalize">{user?.role || 'user'}</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Membro desde</label>
              <span className="text-white">
                {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityPage;
