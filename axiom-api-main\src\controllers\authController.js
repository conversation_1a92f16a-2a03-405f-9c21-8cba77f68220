const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Simulação de banco de dados em memória (em produção, usar um banco real)
const users = [];

// Chave secreta para JWT (em produção, usar variável de ambiente)
const JWT_SECRET = process.env.JWT_SECRET || 'axiom-api-secret-key-2024';

// Função para gerar token JWT
const generateToken = (userId, email, name) => {
    return jwt.sign(
        { 
            id: userId, 
            email: email,
            name: name 
        },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
};

// Registro de usuário
const register = async (req, res) => {
    try {
        const { name, email, whatsappNumber, password } = req.body;

        // Validação básica
        if (!name || !email || !password) {
            return res.status(400).json({
                message: 'Nome, email e senha são obrigatórios'
            });
        }

        if (password.length < 8) {
            return res.status(400).json({
                message: 'A senha deve ter pelo menos 8 caracteres'
            });
        }

        // Verificar se o usuário já existe
        const existingUser = users.find(user => user.email === email);
        if (existingUser) {
            return res.status(409).json({
                message: 'Este e-mail já está em uso'
            });
        }

        // Criptografar a senha
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Criar novo usuário
        const newUser = {
            id: Date.now().toString(), // ID simples para demo
            name,
            email,
            whatsappNumber,
            password: hashedPassword,
            createdAt: new Date().toISOString()
        };

        // Adicionar ao "banco de dados"
        users.push(newUser);

        console.log(`[AUTH] Novo usuário registrado: ${email}`);

        res.status(201).json({
            message: 'Usuário criado com sucesso',
            user: {
                id: newUser.id,
                name: newUser.name,
                email: newUser.email,
                whatsappNumber: newUser.whatsappNumber
            }
        });

    } catch (error) {
        console.error('[AUTH] Erro no registro:', error);
        res.status(500).json({
            message: 'Erro interno do servidor'
        });
    }
};

// Login de usuário
const login = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Validação básica
        if (!email || !password) {
            return res.status(400).json({
                message: 'Email e senha são obrigatórios'
            });
        }

        // Buscar usuário
        const user = users.find(u => u.email === email);
        if (!user) {
            return res.status(404).json({
                message: 'Usuário não encontrado'
            });
        }

        // Verificar senha
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return res.status(401).json({
                message: 'Senha incorreta'
            });
        }

        // Gerar token
        const token = generateToken(user.id, user.email, user.name);

        console.log(`[AUTH] Login realizado: ${email}`);

        res.status(200).json({
            message: 'Login realizado com sucesso',
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                whatsappNumber: user.whatsappNumber
            }
        });

    } catch (error) {
        console.error('[AUTH] Erro no login:', error);
        res.status(500).json({
            message: 'Erro interno do servidor'
        });
    }
};

// Middleware para verificar token
const verifyToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({
            message: 'Token de acesso requerido'
        });
    }

    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({
            message: 'Token inválido'
        });
    }
};

// Função para listar usuários (apenas para debug)
const getUsers = (req, res) => {
    const safeUsers = users.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        whatsappNumber: user.whatsappNumber,
        createdAt: user.createdAt
    }));
    
    res.json({
        message: 'Lista de usuários',
        users: safeUsers,
        total: users.length
    });
};

module.exports = {
    register,
    login,
    verifyToken,
    getUsers
};
