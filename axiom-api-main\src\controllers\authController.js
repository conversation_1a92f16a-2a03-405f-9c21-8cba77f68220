const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// Chave secreta para JWT
const JWT_SECRET = process.env.JWT_SECRET || 'axiom-api-secret-key-2024';

// Função para gerar token JWT
const generateToken = (userId, email, name) => {
    return jwt.sign(
        { 
            id: userId, 
            email: email,
            name: name 
        },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
};

// Registro de usuário
const register = async (req, res) => {
    try {
        const { name, email, whatsappNumber, password } = req.body;

        // Validação básica
        if (!name || !email || !password) {
            return res.status(400).json({
                message: 'Nome, email e senha são obrigatórios'
            });
        }

        if (password.length < 8) {
            return res.status(400).json({
                message: 'A senha deve ter pelo menos 8 caracteres'
            });
        }

        // Verificar se o usuário já existe
        const existingUserResult = await query(
            'SELECT id FROM users WHERE email = $1',
            [email]
        );

        if (existingUserResult.rows.length > 0) {
            return res.status(409).json({
                message: 'Este e-mail já está em uso'
            });
        }

        // Criptografar a senha
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Criar novo usuário no banco
        const newUserResult = await query(
            'INSERT INTO users (name, email, whatsapp_number, password, role) VALUES ($1, $2, $3, $4, $5) RETURNING id, name, email, whatsapp_number, role, created_at',
            [name, email, whatsappNumber, hashedPassword, 'user']
        );

        const newUser = newUserResult.rows[0];

        console.log(`[AUTH] Novo usuário registrado: ${email}`);

        res.status(201).json({
            message: 'Usuário criado com sucesso',
            user: {
                id: newUser.id,
                name: newUser.name,
                email: newUser.email,
                whatsappNumber: newUser.whatsapp_number,
                role: newUser.role,
                createdAt: newUser.created_at
            }
        });

    } catch (error) {
        console.error('[AUTH] Erro no registro:', error);
        res.status(500).json({
            message: 'Erro interno do servidor'
        });
    }
};

// Login de usuário
const login = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Validação básica
        if (!email || !password) {
            return res.status(400).json({
                message: 'Email e senha são obrigatórios'
            });
        }

        // Buscar usuário no banco
        const userResult = await query(
            'SELECT id, name, email, whatsapp_number, password, role FROM users WHERE email = $1',
            [email]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                message: 'Usuário não encontrado'
            });
        }

        const user = userResult.rows[0];

        // Verificar senha
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return res.status(401).json({
                message: 'Senha incorreta'
            });
        }

        // Gerar token
        const token = generateToken(user.id, user.email, user.name);

        console.log(`[AUTH] Login realizado: ${email}`);

        res.status(200).json({
            message: 'Login realizado com sucesso',
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                whatsappNumber: user.whatsapp_number,
                role: user.role
            }
        });

    } catch (error) {
        console.error('[AUTH] Erro no login:', error);
        res.status(500).json({
            message: 'Erro interno do servidor'
        });
    }
};

// Middleware para verificar token
const verifyToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({
            message: 'Token de acesso requerido'
        });
    }

    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({
            message: 'Token inválido'
        });
    }
};

// Função para listar usuários (apenas para debug/admin)
const getUsers = async (req, res) => {
    try {
        const usersResult = await query(
            'SELECT id, name, email, whatsapp_number, role, created_at FROM users ORDER BY created_at DESC'
        );

        res.json({
            message: 'Lista de usuários',
            users: usersResult.rows,
            total: usersResult.rows.length
        });
    } catch (error) {
        console.error('[AUTH] Erro ao buscar usuários:', error);
        res.status(500).json({
            message: 'Erro interno do servidor'
        });
    }
};

module.exports = {
    register,
    login,
    verifyToken,
    getUsers
};
