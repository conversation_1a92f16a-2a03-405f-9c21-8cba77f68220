-- =====================================================
-- AXIOM API - ESTRUTURA MULTI-TENANT POSTGRESQL
-- =====================================================

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- TABELA DE USUÁRIOS
-- =====================================================

CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    whatsapp_number VARCHAR(20),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    api_token VARCHAR(255) UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_api_token ON users(api_token);

-- =====================================================
-- TABELA DE INSTÂNCIAS (MULTI-TENANT)
-- =====================================================

CREATE TABLE IF NOT EXISTS instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    instance_id VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'CREATED' CHECK (status IN ('CREATED', 'CONNECTING', 'CONNECTED', 'DISCONNECTED', 'ERROR', 'DELETED')),
    webhook_url VARCHAR(500),
    qr_code TEXT,
    session_data JSONB,
    settings JSONB DEFAULT '{}',
    last_activity TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices críticos para multi-tenancy e performance
CREATE INDEX IF NOT EXISTS idx_instances_owner_id ON instances(owner_id);
CREATE INDEX IF NOT EXISTS idx_instances_instance_id ON instances(instance_id);
CREATE INDEX IF NOT EXISTS idx_instances_status ON instances(status);
CREATE INDEX IF NOT EXISTS idx_instances_created_at ON instances(created_at);

-- =====================================================
-- TABELA DE MENSAGENS (HISTÓRICO)
-- =====================================================

CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instance_id UUID NOT NULL REFERENCES instances(id) ON DELETE CASCADE,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message_id VARCHAR(255),
    chat_id VARCHAR(255) NOT NULL,
    from_number VARCHAR(50),
    to_number VARCHAR(50),
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'video', 'audio', 'document', 'location', 'contact')),
    content TEXT,
    media_url VARCHAR(500),
    status VARCHAR(50) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    direction VARCHAR(20) DEFAULT 'outbound' CHECK (direction IN ('inbound', 'outbound')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices para performance de consultas
CREATE INDEX IF NOT EXISTS idx_messages_instance_id ON messages(instance_id);
CREATE INDEX IF NOT EXISTS idx_messages_owner_id ON messages(owner_id);
CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_direction ON messages(direction);

-- =====================================================
-- FUNÇÕES E TRIGGERS
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_instances_updated_at ON instances;
CREATE TRIGGER update_instances_updated_at 
    BEFORE UPDATE ON instances 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para gerar API token único
CREATE OR REPLACE FUNCTION generate_api_token()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.api_token IS NULL THEN
        NEW.api_token := 'axiom_' || encode(gen_random_bytes(32), 'hex');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para gerar API token automaticamente
DROP TRIGGER IF EXISTS generate_user_api_token ON users;
CREATE TRIGGER generate_user_api_token
    BEFORE INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION generate_api_token();

-- =====================================================
-- POLÍTICAS DE SEGURANÇA (RLS)
-- =====================================================

-- Habilitar Row Level Security
ALTER TABLE instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Política para instances: usuários só veem suas próprias instâncias
CREATE POLICY instances_isolation ON instances
    FOR ALL
    TO public
    USING (owner_id = current_setting('app.current_user_id', true)::UUID);

-- Política para messages: usuários só veem mensagens de suas instâncias
CREATE POLICY messages_isolation ON messages
    FOR ALL
    TO public
    USING (owner_id = current_setting('app.current_user_id', true)::UUID);

-- =====================================================
-- VIEWS ÚTEIS PARA DASHBOARD
-- =====================================================

-- View para estatísticas de usuários
CREATE OR REPLACE VIEW user_dashboard_stats AS
SELECT 
    u.id,
    u.name,
    u.email,
    COUNT(i.id) as total_instances,
    COUNT(CASE WHEN i.status = 'CONNECTED' THEN 1 END) as connected_instances,
    COUNT(CASE WHEN i.status = 'CREATED' THEN 1 END) as pending_instances,
    COUNT(CASE WHEN i.status = 'DISCONNECTED' THEN 1 END) as disconnected_instances,
    COUNT(m.id) as total_messages,
    COUNT(CASE WHEN m.direction = 'outbound' THEN 1 END) as sent_messages,
    COUNT(CASE WHEN m.direction = 'inbound' THEN 1 END) as received_messages,
    u.created_at as user_since
FROM users u
LEFT JOIN instances i ON u.id = i.owner_id AND i.status != 'DELETED'
LEFT JOIN messages m ON u.id = m.owner_id AND m.created_at >= CURRENT_DATE - INTERVAL '30 days'
WHERE u.is_active = true
GROUP BY u.id, u.name, u.email, u.created_at;

-- View para atividade recente de instâncias
CREATE OR REPLACE VIEW recent_instance_activity AS
SELECT 
    i.id,
    i.name,
    i.instance_id,
    i.status,
    i.owner_id,
    i.last_activity,
    i.created_at,
    u.name as owner_name,
    COUNT(m.id) as recent_messages
FROM instances i
JOIN users u ON i.owner_id = u.id
LEFT JOIN messages m ON i.id = m.instance_id AND m.created_at >= CURRENT_DATE - INTERVAL '7 days'
WHERE i.status != 'DELETED'
GROUP BY i.id, i.name, i.instance_id, i.status, i.owner_id, i.last_activity, i.created_at, u.name
ORDER BY i.last_activity DESC NULLS LAST, i.created_at DESC;

-- =====================================================
-- DADOS INICIAIS (SEEDS)
-- =====================================================

-- Inserir usuário administrador padrão (apenas se não existir)
INSERT INTO users (id, name, email, password, role, is_active, email_verified)
SELECT 
    uuid_generate_v4(),
    'Administrador Axiom',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'admin123'
    'admin',
    true,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
);

-- =====================================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON TABLE users IS 'Tabela de usuários do sistema multi-tenant Axiom API';
COMMENT ON TABLE instances IS 'Instâncias WhatsApp por usuário com isolamento de dados';
COMMENT ON TABLE messages IS 'Histórico de mensagens por instância e usuário';

COMMENT ON COLUMN users.api_token IS 'Token único para autenticação de API externa';
COMMENT ON COLUMN instances.owner_id IS 'Chave estrangeira para isolamento multi-tenant';
COMMENT ON COLUMN instances.status IS 'Status da instância: CREATED, CONNECTING, CONNECTED, DISCONNECTED, ERROR, DELETED';
COMMENT ON COLUMN instances.instance_id IS 'ID único da instância para integração com orquestrador Docker';
COMMENT ON COLUMN messages.direction IS 'Direção da mensagem: inbound (recebida) ou outbound (enviada)';

-- =====================================================
-- VERIFICAÇÕES DE INTEGRIDADE
-- =====================================================

-- Verificar se as tabelas foram criadas corretamente
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        RAISE EXCEPTION 'Tabela users não foi criada corretamente';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'instances') THEN
        RAISE EXCEPTION 'Tabela instances não foi criada corretamente';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        RAISE EXCEPTION 'Tabela messages não foi criada corretamente';
    END IF;
    
    RAISE NOTICE 'Estrutura do banco de dados criada com sucesso!';
END $$;
