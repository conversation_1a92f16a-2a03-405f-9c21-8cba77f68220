const express = require('express');
const { register, login, getUsers } = require('../controllers/authController');
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

const router = express.Router();

// Rota de registro
router.post('/auth/register', register);

// Rota de login
router.post('/auth/login', login);

// Rota para listar usuários (apenas para admin)
router.get('/auth/users', authenticateToken, requireAdmin, getUsers);

// Rota protegida para perfil do usuário
router.get('/auth/profile', authenticateToken, (req, res) => {
    res.json({
        message: 'Perfil do usuário',
        user: {
            id: req.user.id,
            name: req.user.name,
            email: req.user.email,
            role: req.user.role,
            createdAt: req.user.created_at
        }
    });
});

// GET /auth/api-token - Buscar token de API do usuário
router.get('/auth/api-token', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;

        const userResult = await query(
            'SELECT api_token FROM users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Usuário não encontrado',
                message: 'Usuário não existe'
            });
        }

        res.json({
            success: true,
            message: 'Token de API recuperado com sucesso',
            api_token: userResult.rows[0].api_token
        });

    } catch (error) {
        console.error('[AUTH] Erro ao buscar token de API:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar token de API'
        });
    }
});

module.exports = router;
