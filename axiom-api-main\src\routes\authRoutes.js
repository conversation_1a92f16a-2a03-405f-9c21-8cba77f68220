const express = require('express');
const { register, login, getUsers } = require('../controllers/authController');
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

const router = express.Router();

// Rota de registro
router.post('/auth/register', register);

// Rota de login
router.post('/auth/login', login);

// Rota para listar usuários (apenas para admin)
router.get('/auth/users', authenticateToken, requireAdmin, getUsers);

// Rota protegida para perfil do usuário
router.get('/auth/profile', authenticateToken, (req, res) => {
    res.json({
        message: 'Perfil do usuário',
        user: {
            id: req.user.id,
            name: req.user.name,
            email: req.user.email,
            role: req.user.role,
            createdAt: req.user.created_at
        }
    });
});

module.exports = router;
