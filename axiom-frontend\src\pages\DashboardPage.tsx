import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import {
  Smartphone,
  MessageSquare,
  TrendingUp,
  Users,
  Plus,
  Send,
  BarChart3
} from 'lucide-react';
import api from '../services/apiService';

interface DashboardMetrics {
  totalInstances: number;
  connectedInstances: number;
  totalSent: number;
  totalReceived: number;
  instances: any[];
}

interface ChartData {
  name: string;
  enviado: number;
  recebido: number;
}

const DashboardPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalInstances: 0,
    connectedInstances: 0,
    totalSent: 0,
    totalReceived: 0,
    instances: []
  });
  const [loading, setLoading] = useState(true);

  // Dados mockados para o gráfico
  const chartData: ChartData[] = [
    { name: 'Jan', enviado: 400, recebido: 240 },
    { name: 'Fev', enviado: 300, recebido: 139 },
    { name: 'Mar', enviado: 200, recebido: 980 },
    { name: 'Abr', enviado: 278, recebido: 390 },
    { name: 'Mai', enviado: 189, recebido: 480 },
    { name: 'Jun', enviado: 239, recebido: 380 },
    { name: 'Jul', enviado: 349, recebido: 430 },
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Buscar instâncias do usuário
      const instancesResponse = await api.get('/instances');
      const instances = instancesResponse.data.instances || [];

      // Calcular métricas baseadas nas instâncias
      const connectedInstances = instances.filter((instance: any) =>
        instance.status === 'CONNECTED'
      ).length;

      // Simular dados de mensagens (em produção, viria de endpoints específicos)
      const totalSent = Math.floor(Math.random() * 5000) + 1000;
      const totalReceived = Math.floor(Math.random() * 3000) + 500;

      setMetrics({
        totalInstances: instances.length,
        connectedInstances,
        totalSent,
        totalReceived,
        instances
      });
    } catch (error) {
      console.error('Erro ao buscar dados do dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInstance = () => {
    navigate('/instances');
  };

  const handleViewInstances = () => {
    navigate('/instances');
  };

  const handleViewReports = () => {
    // Futura implementação de relatórios
    alert('Relatórios em desenvolvimento!');
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-xl p-6">
                <div className="h-16 bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-gray-400">
          Bem-vindo de volta, {user?.name}! Aqui está um resumo da sua conta.
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <Smartphone className="w-6 h-6 text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total de Instâncias</p>
              <p className="text-2xl font-bold text-white">{metrics.totalInstances}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="p-3 bg-green-500/20 rounded-lg">
              <Users className="w-6 h-6 text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Instâncias Conectadas</p>
              <p className="text-2xl font-bold text-white">{metrics.connectedInstances}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="p-3 bg-purple-500/20 rounded-lg">
              <Send className="w-6 h-6 text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Enviado</p>
              <p className="text-2xl font-bold text-white">{metrics.totalSent.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-500/20 rounded-lg">
              <MessageSquare className="w-6 h-6 text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Recebido</p>
              <p className="text-2xl font-bold text-white">{metrics.totalReceived.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2 bg-gray-800 border border-gray-700 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Mensagens no Último Mês</h2>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-gray-400">Enviado</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-gray-400">Recebido</span>
              </div>
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="name"
                  stroke="#9CA3AF"
                  fontSize={12}
                />
                <YAxis
                  stroke="#9CA3AF"
                  fontSize={12}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="enviado"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="recebido"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Atividade Recente</h2>
          <div className="space-y-4">
            {metrics.instances.slice(0, 3).map((instance, index) => (
              <div key={instance.id || index} className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  instance.status === 'CONNECTED' ? 'bg-green-500' :
                  instance.status === 'CREATED' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {instance.name || 'Instância sem nome'}
                  </p>
                  <p className="text-xs text-gray-400">
                    {instance.status === 'CONNECTED' ? 'Conectada' :
                     instance.status === 'CREATED' ? 'Aguardando QR' : 'Desconectada'}
                  </p>
                </div>
              </div>
            ))}
            {metrics.instances.length === 0 && (
              <div className="text-center py-4">
                <p className="text-gray-400 text-sm">Nenhuma instância criada ainda</p>
                <button
                  onClick={handleCreateInstance}
                  className="mt-2 text-blue-400 hover:text-blue-300 text-sm font-medium"
                >
                  Criar primeira instância
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 border border-gray-700 rounded-xl p-6">
        <h2 className="text-xl font-bold text-white mb-6">Ações Rápidas</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleCreateInstance}
            className="flex items-center justify-center p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors group"
          >
            <Plus className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
            Nova Instância
          </button>
          <button
            onClick={handleViewInstances}
            className="flex items-center justify-center p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white font-medium transition-colors group"
          >
            <Smartphone className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
            Gerenciar Instâncias
          </button>
          <button
            onClick={handleViewReports}
            className="flex items-center justify-center p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors group"
          >
            <BarChart3 className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
            Ver Relatórios
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
