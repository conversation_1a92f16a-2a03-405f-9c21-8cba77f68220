-- =====================================================
-- AXIOM API - ESTRUTURA DO BANCO DE DADOS MULTI-TENANT
-- =====================================================

-- Extensõ<PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- TABELA DE USUÁRIOS
-- =====================================================

-- Criar tabela users se não existir
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    whatsapp_number VARCHAR(20),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Adicionar coluna role se não existir (para compatibilidade)
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(50) DEFAULT 'user';

-- Adicionar índices para performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- =====================================================
-- TABELA DE INSTÂNCIAS
-- =====================================================

-- Criar tabela instances
CREATE TABLE IF NOT EXISTS instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    instance_id VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'CREATED' CHECK (status IN ('CREATED', 'CONNECTING', 'CONNECTED', 'DISCONNECTED', 'ERROR', 'DELETED')),
    webhook_url VARCHAR(500),
    api_key VARCHAR(255),
    qr_code TEXT,
    session_data JSONB,
    settings JSONB DEFAULT '{}',
    last_activity TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices para performance e segurança
CREATE INDEX IF NOT EXISTS idx_instances_owner_id ON instances(owner_id);
CREATE INDEX IF NOT EXISTS idx_instances_instance_id ON instances(instance_id);
CREATE INDEX IF NOT EXISTS idx_instances_status ON instances(status);
CREATE INDEX IF NOT EXISTS idx_instances_created_at ON instances(created_at);

-- =====================================================
-- TABELA DE MENSAGENS (PARA LOGS E HISTÓRICO)
-- =====================================================

CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instance_id UUID NOT NULL REFERENCES instances(id) ON DELETE CASCADE,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message_id VARCHAR(255),
    chat_id VARCHAR(255) NOT NULL,
    from_number VARCHAR(50),
    to_number VARCHAR(50),
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'video', 'audio', 'document', 'location', 'contact')),
    content TEXT,
    media_url VARCHAR(500),
    status VARCHAR(50) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    direction VARCHAR(20) DEFAULT 'outbound' CHECK (direction IN ('inbound', 'outbound')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_messages_instance_id ON messages(instance_id);
CREATE INDEX IF NOT EXISTS idx_messages_owner_id ON messages(owner_id);
CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_direction ON messages(direction);

-- =====================================================
-- TABELA DE TOKENS DE API (PARA AUTENTICAÇÃO)
-- =====================================================

CREATE TABLE IF NOT EXISTS api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    permissions JSONB DEFAULT '[]',
    last_used TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_api_tokens_user_id ON api_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_api_tokens_hash ON api_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_api_tokens_active ON api_tokens(is_active);

-- =====================================================
-- FUNÇÕES E TRIGGERS
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_instances_updated_at ON instances;
CREATE TRIGGER update_instances_updated_at 
    BEFORE UPDATE ON instances 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POLÍTICAS DE SEGURANÇA (RLS - ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas tabelas sensíveis
ALTER TABLE instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_tokens ENABLE ROW LEVEL SECURITY;

-- Política para instances: usuários só veem suas próprias instâncias
CREATE POLICY instances_isolation ON instances
    FOR ALL
    TO public
    USING (owner_id = current_setting('app.current_user_id')::UUID);

-- Política para messages: usuários só veem mensagens de suas instâncias
CREATE POLICY messages_isolation ON messages
    FOR ALL
    TO public
    USING (owner_id = current_setting('app.current_user_id')::UUID);

-- Política para api_tokens: usuários só veem seus próprios tokens
CREATE POLICY api_tokens_isolation ON api_tokens
    FOR ALL
    TO public
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- =====================================================
-- DADOS INICIAIS (SEEDS)
-- =====================================================

-- Inserir usuário administrador padrão (apenas se não existir)
INSERT INTO users (id, name, email, password, role, is_active, email_verified)
SELECT 
    gen_random_uuid(),
    'Administrador',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'admin123'
    'admin',
    true,
    true
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
);

-- =====================================================
-- VIEWS ÚTEIS
-- =====================================================

-- View para estatísticas de usuários
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.name,
    u.email,
    u.role,
    COUNT(i.id) as total_instances,
    COUNT(CASE WHEN i.status = 'CONNECTED' THEN 1 END) as connected_instances,
    COUNT(m.id) as total_messages,
    u.created_at
FROM users u
LEFT JOIN instances i ON u.id = i.owner_id
LEFT JOIN messages m ON u.id = m.owner_id
GROUP BY u.id, u.name, u.email, u.role, u.created_at;

-- View para estatísticas de instâncias
CREATE OR REPLACE VIEW instance_stats AS
SELECT 
    i.id,
    i.name,
    i.instance_id,
    i.status,
    u.name as owner_name,
    u.email as owner_email,
    COUNT(m.id) as total_messages,
    COUNT(CASE WHEN m.direction = 'outbound' THEN 1 END) as sent_messages,
    COUNT(CASE WHEN m.direction = 'inbound' THEN 1 END) as received_messages,
    i.created_at,
    i.last_activity
FROM instances i
JOIN users u ON i.owner_id = u.id
LEFT JOIN messages m ON i.id = m.instance_id
GROUP BY i.id, i.name, i.instance_id, i.status, u.name, u.email, i.created_at, i.last_activity;

-- =====================================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON TABLE users IS 'Tabela de usuários do sistema multi-tenant';
COMMENT ON TABLE instances IS 'Tabela de instâncias WhatsApp por usuário';
COMMENT ON TABLE messages IS 'Histórico de mensagens por instância';
COMMENT ON TABLE api_tokens IS 'Tokens de API para autenticação';

COMMENT ON COLUMN users.role IS 'Papel do usuário: user, admin, moderator';
COMMENT ON COLUMN instances.status IS 'Status da instância: CREATED, CONNECTING, CONNECTED, DISCONNECTED, ERROR, DELETED';
COMMENT ON COLUMN instances.session_data IS 'Dados da sessão WhatsApp em formato JSON';
COMMENT ON COLUMN messages.direction IS 'Direção da mensagem: inbound (recebida) ou outbound (enviada)';
