const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// Middleware para verificar autenticação JWT
const authenticateToken = async (req, res, next) => {
    try {
        // Extrair token do cabeçalho Authorization
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                error: 'Token de acesso requerido',
                message: 'Você precisa estar logado para acessar este recurso'
            });
        }

        // Verificar e decodificar o token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Buscar dados atualizados do usuário no banco
        const userResult = await query(
            'SELECT id, name, email, role, created_at FROM users WHERE id = $1',
            [decoded.id]
        );

        if (userResult.rows.length === 0) {
            return res.status(401).json({
                error: 'Usuário não encontrado',
                message: 'Token válido mas usuário não existe mais'
            });
        }

        // Anexar dados do usuário à requisição
        req.user = userResult.rows[0];
        
        console.log(`[AUTH] Usuário autenticado: ${req.user.email} (${req.user.id})`);
        
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                error: 'Token inválido',
                message: 'O token fornecido não é válido'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                error: 'Token expirado',
                message: 'Seu token expirou, faça login novamente'
            });
        }

        console.error('[AUTH] Erro no middleware de autenticação:', error);
        return res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao verificar autenticação'
        });
    }
};

// Middleware para verificar se o usuário é administrador
const requireAdmin = (req, res, next) => {
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            error: 'Acesso negado',
            message: 'Você precisa ser administrador para acessar este recurso'
        });
    }
    next();
};

// Middleware para verificar se o usuário é dono do recurso ou admin
const requireOwnershipOrAdmin = (resourceOwnerIdField = 'owner_id') => {
    return (req, res, next) => {
        const resourceOwnerId = req.params[resourceOwnerIdField] || req.body[resourceOwnerIdField];
        
        if (req.user.role === 'admin' || req.user.id === resourceOwnerId) {
            next();
        } else {
            return res.status(403).json({
                error: 'Acesso negado',
                message: 'Você só pode acessar seus próprios recursos'
            });
        }
    };
};

// Middleware opcional para extrair informações do usuário sem exigir autenticação
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (token) {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const userResult = await query(
                'SELECT id, name, email, role FROM users WHERE id = $1',
                [decoded.id]
            );

            if (userResult.rows.length > 0) {
                req.user = userResult.rows[0];
            }
        }
        
        next();
    } catch (error) {
        // Em caso de erro, continua sem usuário autenticado
        next();
    }
};

module.exports = {
    authenticateToken,
    requireAdmin,
    requireOwnershipOrAdmin,
    optionalAuth
};
