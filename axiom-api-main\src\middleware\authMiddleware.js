const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// Middleware para verificar autenticação JWT
const authenticateToken = async (req, res, next) => {
    try {
        // Extrair token do cabeçalho Authorization
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                error: 'Token de acesso requerido',
                message: 'Você precisa estar logado para acessar este recurso'
            });
        }

        // Verificar e decodificar o token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Buscar dados atualizados do usuário no banco
        const userResult = await query(
            'SELECT id, name, email, role, created_at FROM users WHERE id = $1',
            [decoded.id]
        );

        if (userResult.rows.length === 0) {
            return res.status(401).json({
                error: 'Usuário não encontrado',
                message: 'Token válido mas usuário não existe mais'
            });
        }

        // Anexar dados do usuário à requisição
        req.user = userResult.rows[0];
        
        console.log(`[AUTH] Usuário autenticado: ${req.user.email} (${req.user.id})`);
        
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                error: 'Token inválido',
                message: 'O token fornecido não é válido'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                error: 'Token expirado',
                message: 'Seu token expirou, faça login novamente'
            });
        }

        console.error('[AUTH] Erro no middleware de autenticação:', error);
        return res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao verificar autenticação'
        });
    }
};

// Middleware para verificar se o usuário é administrador
const requireAdmin = (req, res, next) => {
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            error: 'Acesso negado',
            message: 'Você precisa ser administrador para acessar este recurso'
        });
    }
    next();
};

// Middleware para verificar se o usuário é dono do recurso ou admin
const requireOwnershipOrAdmin = (resourceOwnerIdField = 'owner_id') => {
    return (req, res, next) => {
        const resourceOwnerId = req.params[resourceOwnerIdField] || req.body[resourceOwnerIdField];
        
        if (req.user.role === 'admin' || req.user.id === resourceOwnerId) {
            next();
        } else {
            return res.status(403).json({
                error: 'Acesso negado',
                message: 'Você só pode acessar seus próprios recursos'
            });
        }
    };
};

// Middleware opcional para extrair informações do usuário sem exigir autenticação
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (token) {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const userResult = await query(
                'SELECT id, name, email, role FROM users WHERE id = $1',
                [decoded.id]
            );

            if (userResult.rows.length > 0) {
                req.user = userResult.rows[0];
            }
        }
        
        next();
    } catch (error) {
        // Em caso de erro, continua sem usuário autenticado
        next();
    }
};

// Middleware para verificar ownership de instâncias
const requireInstanceOwnership = async (req, res, next) => {
    try {
        const instanceId = req.params.id || req.params.instanceId;

        if (!instanceId) {
            return res.status(400).json({
                error: 'ID da instância não fornecido',
                message: 'É necessário fornecer o ID da instância'
            });
        }

        // Administradores podem acessar qualquer instância
        if (req.user.role === 'admin') {
            return next();
        }

        // Verificar se a instância pertence ao usuário
        const instanceResult = await query(
            'SELECT owner_id FROM instances WHERE id = $1',
            [instanceId]
        );

        if (instanceResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        if (instanceResult.rows[0].owner_id !== req.user.id) {
            return res.status(403).json({
                error: 'Acesso negado',
                message: 'Você não tem permissão para acessar esta instância'
            });
        }

        next();
    } catch (error) {
        console.error('[AUTH] Erro ao verificar ownership da instância:', error);
        return res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao verificar permissões da instância'
        });
    }
};

// Função para gerar token JWT
const generateToken = (user) => {
    const payload = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role || 'user'
    };

    return jwt.sign(payload, process.env.JWT_SECRET || 'axiom-secret-key', {
        expiresIn: '24h',
        issuer: 'axiom-api',
        audience: 'axiom-users'
    });
};

// Função para verificar token sem middleware
const verifyToken = (token) => {
    try {
        return jwt.verify(token, process.env.JWT_SECRET || 'axiom-secret-key');
    } catch (error) {
        return null;
    }
};

module.exports = {
    authenticateToken,
    requireAdmin,
    requireOwnershipOrAdmin,
    requireInstanceOwnership,
    optionalAuth,
    generateToken,
    verifyToken
};
