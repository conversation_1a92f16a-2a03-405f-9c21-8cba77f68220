2025/06/26-19:10:41.963 293c Reusing MANIFEST C:\Users\<USER>\Axiom API\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/26-19:10:41.964 293c Recovering log #81
2025/06/26-19:10:41.993 293c Reusing old log C:\Users\<USER>\Axiom API\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000081.log 
2025/06/26-19:10:41.993 293c Delete type=2 #80
2025/06/26-19:10:46.330 1a00 Level-0 table #86: started
2025/06/26-19:10:46.344 1a00 Level-0 table #86: 81447 bytes OK
2025/06/26-19:10:46.347 1a00 Delete type=0 #81
2025/06/26-19:10:46.353 22f8 Manual compaction at level-0 from '\x00 \x00\x00\x00' @ 72057594037927935 : 1 .. '\x00!\x00\x00\x00' @ 0 : 0; will stop at '\x00"\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 7940 : 1
2025/06/26-19:10:46.353 22f8 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.373 22f8 Generated table #87@0: 2894 keys, 41311 bytes
2025/06/26-19:10:46.373 22f8 Compacted 1@0 + 1@1 files => 41311 bytes
2025/06/26-19:10:46.374 22f8 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.376 22f8 Delete type=2 #86
2025/06/26-19:10:46.377 22f8 Manual compaction at level-0 from '\x00"\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 7940 : 1 .. '\x00!\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:46.383 1380 Level-0 table #89: started
2025/06/26-19:10:46.402 1380 Level-0 table #89: 1795 bytes OK
2025/06/26-19:10:46.403 1380 Delete type=2 #83
2025/06/26-19:10:46.403 1380 Delete type=0 #85
2025/06/26-19:10:46.412 1a00 Manual compaction at level-0 from '\x00\x1e\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x1f\x00\x00\x00' @ 0 : 0; will stop at '\x00\x1e\x05\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 10158 : 0
2025/06/26-19:10:46.412 1a00 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.426 1a00 Generated table #90@0: 2818 keys, 40088 bytes
2025/06/26-19:10:46.426 1a00 Compacted 1@0 + 1@1 files => 40088 bytes
2025/06/26-19:10:46.453 1a00 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.454 1a00 Delete type=2 #89
2025/06/26-19:10:46.454 1a00 Manual compaction at level-0 from '\x00\x1e\x05\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 10158 : 0 .. '\x00\x1f\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:46.456 1a00 Level-0 table #92: started
2025/06/26-19:10:46.462 1a00 Level-0 table #92: 4698 bytes OK
2025/06/26-19:10:46.485 1a00 Delete type=2 #87
2025/06/26-19:10:46.485 1a00 Delete type=0 #88
2025/06/26-19:10:46.486 4c0 Manual compaction at level-0 from '\x00!\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00"\x00\x00\x00' @ 0 : 0; will stop at '\x00#\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 10475 : 1
2025/06/26-19:10:46.486 4c0 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.501 4c0 Generated table #93@0: 3105 keys, 43812 bytes
2025/06/26-19:10:46.501 4c0 Compacted 1@0 + 1@1 files => 43812 bytes
2025/06/26-19:10:46.502 4c0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.502 4c0 Delete type=2 #92
2025/06/26-19:10:46.503 4c0 Manual compaction at level-0 from '\x00#\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 10475 : 1 .. '\x00"\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:46.504 22f8 Level-0 table #95: started
2025/06/26-19:10:46.521 22f8 Level-0 table #95: 507 bytes OK
2025/06/26-19:10:46.522 22f8 Delete type=2 #90
2025/06/26-19:10:46.522 22f8 Delete type=0 #91
2025/06/26-19:10:46.525 22f8 Manual compaction at level-0 from '\x00\x1d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x1e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x1d\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 10535 : 0
2025/06/26-19:10:46.525 22f8 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.539 22f8 Generated table #96@0: 3080 keys, 42984 bytes
2025/06/26-19:10:46.539 22f8 Compacted 1@0 + 1@1 files => 42984 bytes
2025/06/26-19:10:46.541 22f8 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.542 22f8 Delete type=2 #95
2025/06/26-19:10:46.542 22f8 Manual compaction at level-0 from '\x00\x1d\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 10535 : 0 .. '\x00\x1e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:46.546 22f8 Level-0 table #98: started
2025/06/26-19:10:46.562 22f8 Level-0 table #98: 500 bytes OK
2025/06/26-19:10:46.563 22f8 Delete type=2 #93
2025/06/26-19:10:46.564 22f8 Delete type=0 #94
2025/06/26-19:10:46.568 22f8 Manual compaction at level-0 from '\x00"\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00#\x00\x00\x00' @ 0 : 0; will stop at '\x00"\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 10560 : 0
2025/06/26-19:10:46.568 22f8 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.584 22f8 Generated table #99@0: 3059 keys, 42714 bytes
2025/06/26-19:10:46.584 22f8 Compacted 1@0 + 1@1 files => 42714 bytes
2025/06/26-19:10:46.585 22f8 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.586 22f8 Delete type=2 #98
2025/06/26-19:10:46.586 1a00 Manual compaction at level-0 from '\x00"\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 10560 : 0 .. '\x00#\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:46.591 22f8 Level-0 table #101: started
2025/06/26-19:10:46.602 22f8 Level-0 table #101: 9715 bytes OK
2025/06/26-19:10:46.604 22f8 Delete type=2 #96
2025/06/26-19:10:46.604 22f8 Delete type=0 #97
2025/06/26-19:10:46.615 22f8 Manual compaction at level-0 from '\x00\x11\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x12\x00\x00\x00' @ 0 : 0; will stop at '\x00\x11\x0a\x02\x03\x00\x00\x00\x00\x00\x00\x00@' @ 11100 : 0
2025/06/26-19:10:46.615 22f8 Compacting 1@0 + 1@1 files
2025/06/26-19:10:46.629 22f8 Generated table #102@0: 2523 keys, 22948 bytes
2025/06/26-19:10:46.629 22f8 Compacted 1@0 + 1@1 files => 22948 bytes
2025/06/26-19:10:46.630 22f8 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:46.633 22f8 Delete type=2 #101
2025/06/26-19:10:46.634 1a00 Manual compaction at level-0 from '\x00\x11\x0a\x02\x03\x00\x00\x00\x00\x00\x00\x00@' @ 11100 : 0 .. '\x00\x12\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:48.725 293c Level-0 table #104: started
2025/06/26-19:10:48.788 293c Level-0 table #104: 19272 bytes OK
2025/06/26-19:10:48.790 293c Delete type=2 #99
2025/06/26-19:10:48.790 293c Delete type=0 #100
2025/06/26-19:10:48.816 293c Manual compaction at level-0 from '\x00$\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00%\x00\x00\x00' @ 0 : 0; will stop at '\x00.\x00\x00\x05' @ 12120 : 1
2025/06/26-19:10:48.816 293c Compacting 1@0 + 1@1 files
2025/06/26-19:10:48.845 293c Generated table #105@0: 3026 keys, 33211 bytes
2025/06/26-19:10:48.845 293c Compacted 1@0 + 1@1 files => 33211 bytes
2025/06/26-19:10:48.846 293c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:48.847 293c Delete type=2 #104
2025/06/26-19:10:48.851 293c Manual compaction at level-0 from '\x00.\x00\x00\x05' @ 12120 : 1 .. '\x00%\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:48.856 14b0 Level-0 table #107: started
2025/06/26-19:10:48.882 14b0 Level-0 table #107: 1578 bytes OK
2025/06/26-19:10:48.883 14b0 Delete type=2 #102
2025/06/26-19:10:48.883 14b0 Delete type=0 #103
2025/06/26-19:10:48.989 4c0 Manual compaction at level-0 from '\x00%\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00&\x00\x00\x00' @ 0 : 0; will stop at '\x00%\x00\x00\x05' @ 12225 : 0
2025/06/26-19:10:48.989 4c0 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.035 4c0 Generated table #108@0: 3051 keys, 34004 bytes
2025/06/26-19:10:49.035 4c0 Compacted 1@0 + 1@1 files => 34004 bytes
2025/06/26-19:10:49.037 4c0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.038 4c0 Delete type=2 #107
2025/06/26-19:10:49.038 4c0 Manual compaction at level-0 from '\x00%\x00\x00\x05' @ 12225 : 0 .. '\x00&\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.040 293c Level-0 table #110: started
2025/06/26-19:10:49.044 293c Level-0 table #110: 4130 bytes OK
2025/06/26-19:10:49.088 293c Delete type=2 #105
2025/06/26-19:10:49.088 293c Delete type=0 #106
2025/06/26-19:10:49.104 528 Manual compaction at level-0 from '\x00&\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00'\x00\x00\x00' @ 0 : 0; will stop at '\x001\x00\x00\x05' @ 12465 : 1
2025/06/26-19:10:49.104 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.124 528 Generated table #111@0: 3238 keys, 37140 bytes
2025/06/26-19:10:49.124 528 Compacted 1@0 + 1@1 files => 37140 bytes
2025/06/26-19:10:49.125 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.126 528 Delete type=2 #110
2025/06/26-19:10:49.126 528 Manual compaction at level-0 from '\x001\x00\x00\x05' @ 12465 : 1 .. '\x00'\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.129 293c Level-0 table #113: started
2025/06/26-19:10:49.150 293c Level-0 table #113: 251 bytes OK
2025/06/26-19:10:49.151 293c Delete type=2 #108
2025/06/26-19:10:49.152 293c Delete type=0 #109
2025/06/26-19:10:49.188 293c Manual compaction at level-0 from '\x00'\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00(\x00\x00\x00' @ 0 : 0; will stop at '\x00'\x00\x00\x05' @ 12485 : 0
2025/06/26-19:10:49.188 293c Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.211 293c Generated table #114@0: 3236 keys, 37098 bytes
2025/06/26-19:10:49.211 293c Compacted 1@0 + 1@1 files => 37098 bytes
2025/06/26-19:10:49.255 293c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.257 293c Delete type=2 #113
2025/06/26-19:10:49.320 2bf0 Manual compaction at level-0 from '\x00'\x00\x00\x05' @ 12485 : 0 .. '\x00(\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.321 528 Level-0 table #116: started
2025/06/26-19:10:49.366 528 Level-0 table #116: 249 bytes OK
2025/06/26-19:10:49.401 528 Delete type=2 #111
2025/06/26-19:10:49.401 528 Delete type=0 #112
2025/06/26-19:10:49.415 528 Manual compaction at level-0 from '\x00(\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00)\x00\x00\x00' @ 0 : 0; will stop at '\x00(\x00\x00\x05' @ 12491 : 0
2025/06/26-19:10:49.415 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.452 528 Generated table #117@0: 3234 keys, 37004 bytes
2025/06/26-19:10:49.452 528 Compacted 1@0 + 1@1 files => 37004 bytes
2025/06/26-19:10:49.454 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.454 528 Delete type=2 #116
2025/06/26-19:10:49.455 528 Manual compaction at level-0 from '\x00(\x00\x00\x05' @ 12491 : 0 .. '\x00)\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.456 528 Level-0 table #119: started
2025/06/26-19:10:49.473 528 Level-0 table #119: 249 bytes OK
2025/06/26-19:10:49.485 528 Delete type=2 #114
2025/06/26-19:10:49.485 528 Delete type=0 #115
2025/06/26-19:10:49.485 528 Manual compaction at level-0 from '\x00)\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00*\x00\x00\x00' @ 0 : 0; will stop at '\x00)\x00\x00\x05' @ 12497 : 0
2025/06/26-19:10:49.485 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.510 528 Generated table #120@0: 3232 keys, 36983 bytes
2025/06/26-19:10:49.510 528 Compacted 1@0 + 1@1 files => 36983 bytes
2025/06/26-19:10:49.511 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.511 528 Delete type=2 #119
2025/06/26-19:10:49.512 14b0 Manual compaction at level-0 from '\x00)\x00\x00\x05' @ 12497 : 0 .. '\x00*\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.513 528 Level-0 table #122: started
2025/06/26-19:10:49.527 528 Level-0 table #122: 249 bytes OK
2025/06/26-19:10:49.534 528 Delete type=2 #117
2025/06/26-19:10:49.534 528 Delete type=0 #118
2025/06/26-19:10:49.536 528 Manual compaction at level-0 from '\x00*\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00+\x00\x00\x00' @ 0 : 0; will stop at '\x00*\x00\x00\x05' @ 12503 : 0
2025/06/26-19:10:49.536 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.550 528 Generated table #123@0: 3230 keys, 36890 bytes
2025/06/26-19:10:49.550 528 Compacted 1@0 + 1@1 files => 36890 bytes
2025/06/26-19:10:49.551 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.551 528 Delete type=2 #122
2025/06/26-19:10:49.552 528 Manual compaction at level-0 from '\x00*\x00\x00\x05' @ 12503 : 0 .. '\x00+\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.553 14b0 Level-0 table #125: started
2025/06/26-19:10:49.577 14b0 Level-0 table #125: 251 bytes OK
2025/06/26-19:10:49.578 14b0 Delete type=2 #120
2025/06/26-19:10:49.578 14b0 Delete type=0 #121
2025/06/26-19:10:49.579 14b0 Manual compaction at level-0 from '\x00.\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00/\x00\x00\x00' @ 0 : 0; will stop at '\x00.\x00\x00\x05' @ 12509 : 0
2025/06/26-19:10:49.579 14b0 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.592 14b0 Generated table #126@0: 3228 keys, 36814 bytes
2025/06/26-19:10:49.592 14b0 Compacted 1@0 + 1@1 files => 36814 bytes
2025/06/26-19:10:49.593 14b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.593 14b0 Delete type=2 #125
2025/06/26-19:10:49.599 2538 Manual compaction at level-0 from '\x00.\x00\x00\x05' @ 12509 : 0 .. '\x00/\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.600 2538 Level-0 table #128: started
2025/06/26-19:10:49.617 2538 Level-0 table #128: 249 bytes OK
2025/06/26-19:10:49.618 2538 Delete type=2 #123
2025/06/26-19:10:49.618 2538 Delete type=0 #124
2025/06/26-19:10:49.633 2538 Manual compaction at level-0 from '\x00,\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00-\x00\x00\x00' @ 0 : 0; will stop at '\x00,\x00\x00\x05' @ 12515 : 0
2025/06/26-19:10:49.633 2538 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.646 2538 Generated table #129@0: 3226 keys, 36748 bytes
2025/06/26-19:10:49.646 2538 Compacted 1@0 + 1@1 files => 36748 bytes
2025/06/26-19:10:49.647 2538 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.648 2538 Delete type=2 #128
2025/06/26-19:10:49.649 2538 Manual compaction at level-0 from '\x00,\x00\x00\x05' @ 12515 : 0 .. '\x00-\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.650 528 Level-0 table #131: started
2025/06/26-19:10:49.667 528 Level-0 table #131: 249 bytes OK
2025/06/26-19:10:49.669 528 Delete type=2 #126
2025/06/26-19:10:49.669 528 Delete type=0 #127
2025/06/26-19:10:49.699 14b0 Manual compaction at level-0 from '\x00-\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00.\x00\x00\x00' @ 0 : 0; will stop at '\x00-\x00\x00\x05' @ 12521 : 0
2025/06/26-19:10:49.699 14b0 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.718 14b0 Generated table #132@0: 3224 keys, 36659 bytes
2025/06/26-19:10:49.718 14b0 Compacted 1@0 + 1@1 files => 36659 bytes
2025/06/26-19:10:49.719 14b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.720 14b0 Delete type=2 #131
2025/06/26-19:10:49.720 14b0 Manual compaction at level-0 from '\x00-\x00\x00\x05' @ 12521 : 0 .. '\x00.\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.721 528 Level-0 table #134: started
2025/06/26-19:10:49.738 528 Level-0 table #134: 249 bytes OK
2025/06/26-19:10:49.739 528 Delete type=2 #129
2025/06/26-19:10:49.740 528 Delete type=0 #130
2025/06/26-19:10:49.741 528 Manual compaction at level-0 from '\x00+\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00,\x00\x00\x00' @ 0 : 0; will stop at '\x00+\x00\x00\x05' @ 12527 : 0
2025/06/26-19:10:49.741 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.779 528 Generated table #135@0: 3222 keys, 36521 bytes
2025/06/26-19:10:49.779 528 Compacted 1@0 + 1@1 files => 36521 bytes
2025/06/26-19:10:49.780 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.781 528 Delete type=2 #134
2025/06/26-19:10:49.782 528 Manual compaction at level-0 from '\x00+\x00\x00\x05' @ 12527 : 0 .. '\x00,\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/26-19:10:49.786 528 Level-0 table #137: started
2025/06/26-19:10:49.807 528 Level-0 table #137: 1199 bytes OK
2025/06/26-19:10:49.808 528 Delete type=2 #132
2025/06/26-19:10:49.809 528 Delete type=0 #133
2025/06/26-19:10:49.810 528 Manual compaction at level-0 from '\x001\x00\x00\x00' @ 72057594037927935 : 1 .. '\x002\x00\x00\x00' @ 0 : 0; will stop at '\x001\x00\x00\x05' @ 12647 : 0
2025/06/26-19:10:49.810 528 Compacting 1@0 + 1@1 files
2025/06/26-19:10:49.823 528 Generated table #138@0: 3220 keys, 35851 bytes
2025/06/26-19:10:49.823 528 Compacted 1@0 + 1@1 files => 35851 bytes
2025/06/26-19:10:49.824 528 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/26-19:10:49.825 528 Delete type=2 #137
2025/06/26-19:10:49.826 4c0 Manual compaction at level-0 from '\x001\x00\x00\x05' @ 12647 : 0 .. '\x002\x00\x00\x00' @ 0 : 0; will stop at (end)
