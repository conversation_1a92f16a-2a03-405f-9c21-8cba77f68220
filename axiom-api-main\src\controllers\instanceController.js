const { query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

// Listar todas as instâncias do usuário logado
const getInstances = async (req, res) => {
    try {
        const userId = req.user.id;
        
        const instancesResult = await query(
            'SELECT id, name, instance_id, instance_token, status, created_at, updated_at FROM instances WHERE owner_id = $1 ORDER BY created_at DESC',
            [userId]
        );

        res.json({
            message: 'Instâncias do usuário',
            instances: instancesResult.rows,
            total: instancesResult.rows.length
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar instâncias:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível buscar as instâncias'
        });
    }
};

// Criar nova instância
const createInstance = async (req, res) => {
    try {
        const { name } = req.body;
        const userId = req.user.id;

        // Validação
        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                error: 'Nome obrigatório',
                message: 'O nome da instância é obrigatório'
            });
        }

        // Verificar se o usuário já tem uma instância com esse nome
        const existingInstanceResult = await query(
            'SELECT id FROM instances WHERE owner_id = $1 AND name = $2',
            [userId, name.trim()]
        );

        if (existingInstanceResult.rows.length > 0) {
            return res.status(409).json({
                error: 'Nome já existe',
                message: 'Você já possui uma instância com este nome'
            });
        }

        // Gerar IDs únicos
        const instanceId = `axiom_${uuidv4().replace(/-/g, '').substring(0, 16)}`;
        const instanceToken = uuidv4();

        // Simular orquestração de container (futuro Docker)
        console.log(`[ORCHESTRATOR] Solicitando a criação de um novo container para a instância ID: ${instanceId} para o usuário ${userId}`);

        // Salvar no banco de dados
        const newInstanceResult = await query(
            'INSERT INTO instances (name, owner_id, instance_id, instance_token, status) VALUES ($1, $2, $3, $4, $5) RETURNING *',
            [name.trim(), userId, instanceId, instanceToken, 'CREATED']
        );

        const newInstance = newInstanceResult.rows[0];

        console.log(`[INSTANCES] Nova instância criada: ${instanceId} para usuário ${req.user.email}`);

        res.status(201).json({
            message: 'Instância criada com sucesso',
            instance: {
                id: newInstance.id,
                name: newInstance.name,
                instanceId: newInstance.instance_id,
                status: newInstance.status,
                createdAt: newInstance.created_at
            }
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao criar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível criar a instância'
        });
    }
};

// Buscar instância específica
const getInstance = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        const instanceResult = await query(
            'SELECT * FROM instances WHERE id = $1 AND owner_id = $2',
            [id, userId]
        );

        if (instanceResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância não existe ou não pertence a você'
            });
        }

        const instance = instanceResult.rows[0];

        res.json({
            message: 'Detalhes da instância',
            instance: {
                id: instance.id,
                name: instance.name,
                instanceId: instance.instance_id,
                status: instance.status,
                qrCode: instance.qr_code,
                webhookUrl: instance.webhook_url,
                createdAt: instance.created_at,
                updatedAt: instance.updated_at
            }
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível buscar a instância'
        });
    }
};

// Obter status da instância
const getInstanceStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        // Verificar se a instância pertence ao usuário
        const instanceResult = await query(
            'SELECT instance_id, status FROM instances WHERE id = $1 AND owner_id = $2',
            [id, userId]
        );

        if (instanceResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância não existe ou não pertence a você'
            });
        }

        const instance = instanceResult.rows[0];

        // Simular consulta ao container (futuro Docker API)
        console.log(`[ORCHESTRATOR] Consultando status do container ${instance.instance_id}`);

        // Por enquanto, retornar o status do banco
        res.json({
            message: 'Status da instância',
            instanceId: instance.instance_id,
            status: instance.status,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar status:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível obter o status da instância'
        });
    }
};

// Obter QR Code da instância
const getInstanceQR = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        // Verificar se a instância pertence ao usuário
        const instanceResult = await query(
            'SELECT instance_id, qr_code, status FROM instances WHERE id = $1 AND owner_id = $2',
            [id, userId]
        );

        if (instanceResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância não existe ou não pertence a você'
            });
        }

        const instance = instanceResult.rows[0];

        // Simular consulta ao QR Code do container
        console.log(`[ORCHESTRATOR] Consultando QR Code do container ${instance.instance_id}`);

        if (!instance.qr_code) {
            return res.status(404).json({
                error: 'QR Code não disponível',
                message: 'QR Code ainda não foi gerado para esta instância'
            });
        }

        res.json({
            message: 'QR Code da instância',
            instanceId: instance.instance_id,
            qrCode: instance.qr_code,
            status: instance.status
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar QR Code:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível obter o QR Code da instância'
        });
    }
};

// Deletar instância
const deleteInstance = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        // Verificar se a instância pertence ao usuário
        const instanceResult = await query(
            'SELECT instance_id FROM instances WHERE id = $1 AND owner_id = $2',
            [id, userId]
        );

        if (instanceResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância não existe ou não pertence a você'
            });
        }

        const instance = instanceResult.rows[0];

        // Simular remoção do container
        console.log(`[ORCHESTRATOR] Solicitando remoção do container ${instance.instance_id}`);

        // Deletar do banco de dados
        await query('DELETE FROM instances WHERE id = $1', [id]);

        console.log(`[INSTANCES] Instância ${instance.instance_id} deletada pelo usuário ${req.user.email}`);

        res.json({
            message: 'Instância deletada com sucesso',
            instanceId: instance.instance_id
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao deletar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Não foi possível deletar a instância'
        });
    }
};

module.exports = {
    getInstances,
    createInstance,
    getInstance,
    getInstanceStatus,
    getInstanceQR,
    deleteInstance
};
