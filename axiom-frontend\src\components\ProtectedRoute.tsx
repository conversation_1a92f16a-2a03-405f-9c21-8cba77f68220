import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('[ProtectedRoute] Estado atual:', { isAuthenticated, loading, user: !!user });

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    console.log('[ProtectedRoute] Carregando...');
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Se não estiver autenticado, redirecionar para login
  if (!isAuthenticated) {
    console.log('[ProtectedRoute] Usuário não autenticado, redirecionando para login');
    return <Navigate to="/login" replace />;
  }

  console.log('[ProtectedRoute] Usuário autenticado, renderizando conteúdo');
  // Se estiver autenticado, renderizar o componente filho
  return <>{children}</>;
};

export default ProtectedRoute;
