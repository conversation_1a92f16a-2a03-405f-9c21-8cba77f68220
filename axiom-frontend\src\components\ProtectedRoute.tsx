import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('[ProtectedRoute] Verificação de acesso:', {
    isAuthenticated,
    loading,
    hasUser: !!user,
    userId: user?.id,
    timestamp: new Date().toISOString()
  });

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    console.log('[ProtectedRoute] Estado de carregamento - aguardando verificação de autenticação');
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Verificação dupla: isAuthenticated E user presente
  if (!isAuthenticated || !user) {
    console.log('[ProtectedRoute] Acesso negado - redirecionando para login:', {
      isAuthenticated,
      hasUser: !!user,
      reason: !isAuthenticated ? 'não autenticado' : 'usuário não encontrado'
    });
    return <Navigate to="/login" replace />;
  }

  console.log('[ProtectedRoute] Acesso autorizado - renderizando conteúdo protegido');
  // Se estiver autenticado, renderizar o componente filho
  return <>{children}</>;
};

export default ProtectedRoute;
