{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../ts/context.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAClC,yDAA2D;AAE3D,qCAA6C;AAa7C,yCAAsF;AACtF,qCAAuC;AAGvC,mCAOgB;AAehB;;GAEG;AACH,MAAM,aAAc,SAAQ,qBAAY;IAQtC,eAAe;IACf,YAAY,IAOX;QACC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAXX,UAAK,GAAG,IAAI,gBAAK,EAAE,CAAA;QAmC5B,gBAAW,GAAG,CAAC,GAAyC,EAAE,EAAE;YAC1D,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC1D,CAAC,CAAA;QAED,gBAAW,GAAG,CAAC,GAAmB,EAAE,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACpD,CAAC,CAAA;QA7BC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QACvE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACxC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAA;IACzB,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAuB,QAAW;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IACpC,CAAC;IAUD,oBAAoB,CAAC,GAA0B;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACzC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC7C,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,eAAe,CAAC,GAAmB;QACjC,OAAO,IAAI,mBAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAC1E,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,yCAAyC;AACzC,MAAa,cAAc;IA8BzB;;OAEG;IACH,YAAY,IAQX;QAxBD,eAAe;QACL,eAAU,GAA8B,SAAS,CAAA;QAC3D,eAAe;QACL,UAAK,GAA8B,SAAS,CAAA;QACtD,eAAe;QACL,WAAM,GAA8B,SAAS,CAAA;QACvD,eAAe;QACL,UAAK,GAA8B,SAAS,CAAA;QACtD,eAAe;QACL,YAAO,GAA8B,SAAS,CAAA;QACxD,eAAe;QACL,YAAO,GAA8B,SAAS,CAAA;QAgrBxD,eAAe;QACL,aAAQ,GAAG,CAAC,KAAK,CAAA,CAAC,gDAAgD;QAC5E,eAAe;QACL,WAAM,GAAG,IAAI,GAAG,EAAgE,CAAA;QAuB1F;;WAEG;QACK,qBAAgB,GAAqB;YAC3C,YAAY,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;gBACjD,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAA;iBACrF;gBAED,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;gBAClC,IAAI,CAAC,EAAE,EAAE;oBACP,2FAA2F;oBAC3F,MAAM,IAAI,KAAK,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAA;iBACnE;gBAED,OAAO,gBAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK;oBAC9D,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAC7B,IAAI,uBAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAC3F,CAAA;oBACD,MAAM,UAAU,GAAG,IAAI,KAAK,CAAgB,IAAI,CAAC,CAAA;oBACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;wBAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;wBAC5D,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAC1B,IAAI,uBAAY,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CACtF,CAAA;qBACF;oBAED,IAAI;wBACF,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAA;wBAC/D,IAAI,MAAM,EAAE;4BACV,IAAI,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;gCACrC,IAAA,gBAAQ,EAAC,aAAa,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;gCACrC,MAAM,MAAM,CAAC,KAAK,CAAA;6BACnB;4BACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,YAAY,mBAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;4BAC/E,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;yBAClE;wBACD,OAAO,CAAmB,CAAA;qBAC3B;oBAAC,OAAO,KAAK,EAAE;wBACd,OAAO,IAAI,CAAC,aAAa,CAAC,KAAc,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAChE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CACtD,CAAA;qBACF;gBACH,CAAC,CAAmB,CAAA;YACtB,CAAC;SACF,CAAA;QAzuBC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC;YAC9B,GAAG,IAAI;YACP,KAAK,EAAE,IAAI,CAAC,OAAO;SACpB,CAAC,CAAA;QACF,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACtD,CAAC;IAED,6EAA6E;IAE7E,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;IAC1B,CAAC;IAED;;;;;OAKG;IACH,OAAO;QACL,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAED,6EAA6E;IAE7E;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC,UAAU,CAAA;SACvB;QAED,uDAAuD;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAA;QACvC,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,yBAAc,CAAC,GAAG,CAAC,CAAC,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB;QAED,kDAAkD;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA;QAClC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,yBAAc,CAAC,GAAG,CAAC,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB;QAED,kDAAkD;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA;QAClC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,yBAAc,CAAC,GAAG,CAAC,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM,CAAA;SACnB;QAED,mDAAmD;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAA;QACnC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,yBAAc,CAAC,GAAG,CAAC,CAAC,CAAA;IAChD,CAAC;IAED;;;;OAIG;IACH,IAAI,MAAM;QACR,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,CAAA;SACpB;QAED,2EAA2E;QAC3E,uBAAuB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAExD,wDAAwD;QACxD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAA;QAEpD,sEAAsE;QACtE,iEAAiE;QACjE,sDAAsD;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,yBAAc,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACpD,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED,6EAA6E;IAE7E;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA;IAClF,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM;aACpB,kBAAkB,CAAC,GAAG,CAAC;aACvB,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAA;QACpF,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,WAA4B;QAC1C,MAAM,GAAG,GAAG,CAAC,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;QAC3F,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM;aACpB,kBAAkB,CAAC,GAAG,CAAC;aACvB,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA;QACvF,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,GAAoB;QAC/B,MAAM,WAAW,GAAG,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QAC3E,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM;aACpB,kBAAkB,CAAC,WAAW,CAAC;aAC/B,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA;QACvF,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;YACxD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAChC,IAAI,CAAC,OAAO,GAAG,IAAI,yBAAc,CAAC,YAAY,CAAC,KAA4B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;SAC3F;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAA;QACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CACjD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAC3E,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,SAAyB;QACjC,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;SACpC;QACD,MAAM,GAAG,GAAG,SAAS;YACnB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC;YAC9D,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,QAAQ;QACN,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACjD,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IA0BD,UAAU,CACR,KAAsF;QAEtF,MAAM,eAAe,GAAG,gBAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAChD,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CACtC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAwB,CAAC,CAAC,CAC7D,CAAA;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAClD,IAAI,CAAC,GAAG,CAAC,KAAK,EACd,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAC9B,CAAA;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAA;YAC7D,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CACxF,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAiB,CAAC,CAC/D,CAAA;YACD,OAAO,IAAI,yCAAsB,CAAC;gBAChC,OAAO,EAAE,IAAI;gBACb,aAAa;gBACb,aAAa;gBACb,YAAY;aACb,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YACxC,KAAK,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAA;SAC3B;QAED,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE,CAC7D,KAAK,YAAY,mBAAQ;gBACvB,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/B,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CACzD,CAAA;SACF;QAED,OAAO,eAAe,CAAA;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,WAAW,CAAC,IAAY,EAAE,EAA2C;QACnE,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;IAC1F,CAAC;IAKD,QAAQ,CAAC,KAAkD;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;QAEtF,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACtC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;aAC1F;YAED,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;gBAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAC/C,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAC7C,CAAA;aACF;SACF;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAA;SACxF;aAAM,IAAI,KAAK,KAAK,SAAS,EAAE;YAC9B,iFAAiF;YACjF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAC/C,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAC7C,CAAA;SACF;QAED,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,6EAA6E;IAE7E;;;;;OAKG;IACH,MAAM,CAAC,MAAqB;QAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;IAC9F,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,MAAqB;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAqB;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;IAC/F,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,MAAqB;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC1C,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CACrE,CAAA;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;QAC1E,OAAO,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAqB;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACvC,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAA;IACzB,CAAC;IAED;;;;;;;;;OASG;IACH,cAAc,CAAC,iBAAgC;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC3C,MAAM,eAAe,GAAG,gBAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAChD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAA;YACpE,MAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAA;YACzE,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAA;QAC1E,CAAC,CAAC,CAAA;QACF,IAAI,eAAe,CAAC,KAAK,EAAE;YACzB,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;SACxC;QAED,OAAO,IAAI,OAAO,CAA8B,CAAC,OAAO,EAAE,EAAE;YAC1D,gBAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;gBACxB,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAChC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;oBACpC,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,IAAI,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;gBAC1C,CAAC,CAAC,CACH,CAAA;gBAED,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;oBACnC,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,IAAI,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;gBAC1C,CAAC,CAAC,CACH,CAAA;gBAED,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;gBACzD,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAA;gBAC3E,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,CAAC,CACjF,CAAC,OAAO,EAAE,CAAA;YACb,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,6EAA6E;IAE7E;;;;;;OAMG;IACH,OAAO,CAAC,MAAqB,EAAE,GAAuB;QACpD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAC7D,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CACrE,CAAA;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;QAE/C,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;;;;OAUG;IACH,OAAO,CAAC,MAAqB,EAAE,GAAuB,EAAE,KAAoB;QAC1E,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,wFAAwF;QACxF,oBAAoB;QACpB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACjD,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAClF,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACR,MAAqB,EACrB,GAAuB,EACvB,UAA+C;QAE/C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,gBAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACxB,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAA;YAE5D,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAA;YAChD,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;YACrD,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;YACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YAC1C,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG;gBACxB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;gBACrE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;YAClB,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG;gBACxB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;gBACrE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;YAElB,IAAI,CAAC,GAAG,CAAC,cAAc,CACrB,IAAI,CAAC,GAAG,CAAC,KAAK,EACd,MAAM,CAAC,KAAK,EACZ,UAAU,CAAC,KAAK,EAChB,KAAK,CAAC,KAAK,EACX,GAAG,CAAC,KAAK,EACT,GAAG,CAAC,KAAK,EACT,YAAY,EACZ,UAAU,EACV,QAAQ,CACT,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,6EAA6E;IAE7E;;;;;;;;;;;;OAYG;IACH,YAAY,CACV,IAAmB,EACnB,OAAsB,EACtB,GAAG,IAAqB;QAExB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM;aAC1B,cAAc,CAAC,IAAI,CAAC;aACpB,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE,CACxB,IAAI,CAAC,GAAG,CAAC,QAAQ,CACf,IAAI,CAAC,GAAG,CAAC,KAAK,EACd,IAAI,CAAC,KAAK,EACV,OAAO,CAAC,KAAK,EACb,IAAI,CAAC,MAAM,EACX,YAAY,CAAC,KAAK,CACnB,CACF,CAAA;QAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACzE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACxD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAA;SACxD;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAA;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,QAAQ,CACN,IAAY,EACZ,WAAmB,SAAS;IAC5B;;;;;OAKG;IACH,OAAqC;QAErC,MAAM,YAAY,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAqB,CAAA;QACxE,MAAM,KAAK,GAAG,IAAA,0BAAkB,EAAC,OAAO,CAAc,CAAA;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM;aAC1B,kBAAkB,CAAC,IAAI,CAAC;aACxB,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACtB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,CACnF,CAAA;QACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACzE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACxD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAA;SACxD;QACD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAA;IAC1D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAA4B;QAChC,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAClD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CACjD,CAAA;IACH,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,GAAuB;QACjD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;SAC3B;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;SAC3B;QAED,2EAA2E;QAC3E,yBAAyB;QACzB,OAAO,IAAI,yBAAc,CAAC,GAAG,CAAC,KAA4B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,EAAoB;QAC5B,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,CAAA;SACnB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAA;SAC3E;IACH,CAAC;IAED,6EAA6E;IAE7E;;;OAGG;IACH,IAAI,CAAC,MAAqB;QACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SAC9B;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SAC9B;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SAC9B;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC/B,OAAO,SAAS,CAAA;SACjB;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SAC9B;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;QAC7F,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;SACvB;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,GAAG,CAAA;SACX;IACH,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAI,MAAuC;QACrD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,MAAM,OAAO,GACX,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAE,MAAM,CAAC,KAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAA;YAC1F,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;YAE/D,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE;gBAC3E,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;gBACtC,MAAM,SAAS,GAAG,IAAI,2BAAkB,CAAC,EAAE,CAAC,CAAA;gBAC5C,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAA;gBAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;iBAC5B;gBAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,SAAS,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,SAAS,EAAE,CAAA;iBAC1E;gBAED,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAA;gBACrD,MAAM,SAAS,CAAA;aAChB;YAED,MAAM,IAAI,2BAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;SAC7C;QAED,OAAO,MAAM,CAAC,KAAK,CAAA;IACrB,CAAC;IAOD,eAAe;IACL,WAAW,CAAC,KAAa;QACjC,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,SAAS,CAAA;SACjB;QACD,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED,eAAe;IACL,WAAW,CAAC,KAAa,EAAE,MAA+C;QAClF,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAA;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,IAAI,GAAG,EAAmD,CAAA;YAClE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;SAC/B;QACD,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IACjC,CAAC;IAiDO,aAAa,CAAC,KAA4B;QAChD,IAAI,KAAK,YAAY,mBAAQ,EAAE;YAC7B,OAAO,KAAK,CAAA;SACb;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC7B,CAAC;CACF;AA5xBD,wCA4xBC", "sourcesContent": ["import { debugLog } from \"./debug\"\nimport { QuickJSDeferredPromise } from \"./deferred-promise\"\nimport type { EitherModule } from \"./emscripten-types\"\nimport { QuickJSUnwrapError } from \"./errors\"\nimport {\n  EvalDetectModule,\n  EvalFlags,\n  JSBorrowedCharPointer,\n  JSContextPointer,\n  JSModuleDefPointer,\n  JSRuntimePointer,\n  JSValueConstPointer,\n  JSValuePointer,\n  JSV<PERSON>uePointerPointer,\n  JSVoidPointer,\n} from \"./types-ffi\"\nimport { Disposable, Lifetime, Scope, StaticLifetime, WeakLifetime } from \"./lifetime\"\nimport { ModuleMemory } from \"./memory\"\nimport { ContextCallbacks, QuickJSModuleCallbacks } from \"./module\"\nimport { QuickJSRuntime } from \"./runtime\"\nimport {\n  ContextEvalOptions,\n  EitherFFI,\n  evalOptionsToFlags,\n  JSValue,\n  PromiseExecutor,\n  QuickJSHandle,\n} from \"./types\"\nimport {\n  LowLevelJavascriptVm,\n  SuccessOrFail,\n  VmCallResult,\n  VmFunctionImplementation,\n  VmPropertyDescriptor,\n} from \"./vm-interface\"\n\n/**\n * Property key for getting or setting a property on a handle with\n * [[QuickJSContext.getProp]], [[QuickJSContext.setProp]], or [[QuickJSContext.defineProp]].\n */\nexport type QuickJSPropertyKey = number | string | QuickJSHandle\n\n/**\n * @private\n */\nclass ContextMemory extends ModuleMemory implements Disposable {\n  readonly owner: QuickJSRuntime\n  readonly ctx: Lifetime<JSContextPointer>\n  readonly rt: Lifetime<JSRuntimePointer>\n  readonly module: EitherModule\n  readonly ffi: EitherFFI\n  readonly scope = new Scope()\n\n  /** @private */\n  constructor(args: {\n    owner: QuickJSRuntime\n    module: EitherModule\n    ffi: EitherFFI\n    ctx: Lifetime<JSContextPointer>\n    rt: Lifetime<JSRuntimePointer>\n    ownedLifetimes?: Disposable[]\n  }) {\n    super(args.module)\n    args.ownedLifetimes?.forEach((lifetime) => this.scope.manage(lifetime))\n    this.owner = args.owner\n    this.module = args.module\n    this.ffi = args.ffi\n    this.rt = args.rt\n    this.ctx = this.scope.manage(args.ctx)\n  }\n\n  get alive() {\n    return this.scope.alive\n  }\n\n  dispose() {\n    return this.scope.dispose()\n  }\n\n  /**\n   * Track `lifetime` so that it is disposed when this scope is disposed.\n   */\n  manage<T extends Disposable>(lifetime: T): T {\n    return this.scope.manage(lifetime)\n  }\n\n  copyJSValue = (ptr: JSValuePointer | JSValueConstPointer) => {\n    return this.ffi.QTS_DupValuePointer(this.ctx.value, ptr)\n  }\n\n  freeJSValue = (ptr: JSValuePointer) => {\n    this.ffi.QTS_FreeValuePointer(this.ctx.value, ptr)\n  }\n\n  consumeJSCharPointer(ptr: JSBorrowedCharPointer): string {\n    const str = this.module.UTF8ToString(ptr)\n    this.ffi.QTS_FreeCString(this.ctx.value, ptr)\n    return str\n  }\n\n  heapValueHandle(ptr: JSValuePointer): JSValue {\n    return new Lifetime(ptr, this.copyJSValue, this.freeJSValue, this.owner)\n  }\n}\n\n/**\n * QuickJSContext wraps a QuickJS Javascript context (JSContext*) within a\n * runtime. The contexts within the same runtime may exchange objects freely.\n * You can think of separate runtimes like different domains in a browser, and\n * the contexts within a runtime like the different windows open to the same\n * domain. The {@link runtime} references the context's runtime.\n *\n * This class's methods return {@link QuickJSHandle}, which wrap C pointers (JSValue*).\n * It's the caller's responsibility to call `.dispose()` on any\n * handles you create to free memory once you're done with the handle.\n *\n * Use {@link QuickJSRuntime.newContext} or {@link QuickJSWASMModule.newContext}\n * to create a new QuickJSContext.\n *\n * Create QuickJS values inside the interpreter with methods like\n * [[newNumber]], [[newString]], [[newArray]], [[newObject]],\n * [[newFunction]], and [[newPromise]].\n *\n * Call [[setProp]] or [[defineProp]] to customize objects. Use those methods\n * with [[global]] to expose the values you create to the interior of the\n * interpreter, so they can be used in [[evalCode]].\n *\n * Use [[evalCode]] or [[callFunction]] to execute Javascript inside the VM. If\n * you're using asynchronous code inside the QuickJSContext, you may need to also\n * call [[executePendingJobs]]. Executing code inside the runtime returns a\n * result object representing successful execution or an error. You must dispose\n * of any such results to avoid leaking memory inside the VM.\n *\n * Implement memory and CPU constraints at the runtime level, using [[runtime]].\n * See {@link QuickJSRuntime} for more information.\n *\n */\n// TODO: Manage own callback registration\nexport class QuickJSContext implements LowLevelJavascriptVm<QuickJSHandle>, Disposable {\n  /**\n   * The runtime that created this context.\n   */\n  public readonly runtime: QuickJSRuntime\n\n  /** @private */\n  protected readonly ctx: Lifetime<JSContextPointer>\n  /** @private */\n  protected readonly rt: Lifetime<JSRuntimePointer>\n  /** @private */\n  protected readonly module: EitherModule\n  /** @private */\n  protected readonly ffi: EitherFFI\n  /** @private */\n  protected memory: ContextMemory\n\n  /** @private */\n  protected _undefined: QuickJSHandle | undefined = undefined\n  /** @private */\n  protected _null: QuickJSHandle | undefined = undefined\n  /** @private */\n  protected _false: QuickJSHandle | undefined = undefined\n  /** @private */\n  protected _true: QuickJSHandle | undefined = undefined\n  /** @private */\n  protected _global: QuickJSHandle | undefined = undefined\n  /** @private */\n  protected _BigInt: QuickJSHandle | undefined = undefined\n\n  /**\n   * Use {@link QuickJS.createVm} to create a QuickJSContext instance.\n   */\n  constructor(args: {\n    module: EitherModule\n    ffi: EitherFFI\n    ctx: Lifetime<JSContextPointer>\n    rt: Lifetime<JSRuntimePointer>\n    runtime: QuickJSRuntime\n    ownedLifetimes?: Disposable[]\n    callbacks: QuickJSModuleCallbacks\n  }) {\n    this.runtime = args.runtime\n    this.module = args.module\n    this.ffi = args.ffi\n    this.rt = args.rt\n    this.ctx = args.ctx\n    this.memory = new ContextMemory({\n      ...args,\n      owner: this.runtime,\n    })\n    args.callbacks.setContextCallbacks(this.ctx.value, this.cToHostCallbacks)\n    this.dump = this.dump.bind(this)\n    this.getString = this.getString.bind(this)\n    this.getNumber = this.getNumber.bind(this)\n    this.resolvePromise = this.resolvePromise.bind(this)\n  }\n\n  // @implement Disposable ----------------------------------------------------\n\n  get alive() {\n    return this.memory.alive\n  }\n\n  /**\n   * Dispose of this VM's underlying resources.\n   *\n   * @throws Calling this method without disposing of all created handles\n   * will result in an error.\n   */\n  dispose() {\n    this.memory.dispose()\n  }\n\n  // Globals ------------------------------------------------------------------\n\n  /**\n   * [`undefined`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined).\n   */\n  get undefined(): QuickJSHandle {\n    if (this._undefined) {\n      return this._undefined\n    }\n\n    // Undefined is a constant, immutable value in QuickJS.\n    const ptr = this.ffi.QTS_GetUndefined()\n    return (this._undefined = new StaticLifetime(ptr))\n  }\n\n  /**\n   * [`null`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/null).\n   */\n  get null(): QuickJSHandle {\n    if (this._null) {\n      return this._null\n    }\n\n    // Null is a constant, immutable value in QuickJS.\n    const ptr = this.ffi.QTS_GetNull()\n    return (this._null = new StaticLifetime(ptr))\n  }\n\n  /**\n   * [`true`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/true).\n   */\n  get true(): QuickJSHandle {\n    if (this._true) {\n      return this._true\n    }\n\n    // True is a constant, immutable value in QuickJS.\n    const ptr = this.ffi.QTS_GetTrue()\n    return (this._true = new StaticLifetime(ptr))\n  }\n\n  /**\n   * [`false`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/false).\n   */\n  get false(): QuickJSHandle {\n    if (this._false) {\n      return this._false\n    }\n\n    // False is a constant, immutable value in QuickJS.\n    const ptr = this.ffi.QTS_GetFalse()\n    return (this._false = new StaticLifetime(ptr))\n  }\n\n  /**\n   * [`global`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects).\n   * A handle to the global object inside the interpreter.\n   * You can set properties to create global variables.\n   */\n  get global(): QuickJSHandle {\n    if (this._global) {\n      return this._global\n    }\n\n    // The global is a JSValue, but since it's lifetime is as long as the VM's,\n    // we should manage it.\n    const ptr = this.ffi.QTS_GetGlobalObject(this.ctx.value)\n\n    // Automatically clean up this reference when we dispose\n    this.memory.manage(this.memory.heapValueHandle(ptr))\n\n    // This isn't technically a static lifetime, but since it has the same\n    // lifetime as the VM, it's okay to fake one since when the VM is\n    // disposed, no other functions will accept the value.\n    this._global = new StaticLifetime(ptr, this.runtime)\n    return this._global\n  }\n\n  // New values ---------------------------------------------------------------\n\n  /**\n   * Converts a Javascript number into a QuickJS value.\n   */\n  newNumber(num: number): QuickJSHandle {\n    return this.memory.heapValueHandle(this.ffi.QTS_NewFloat64(this.ctx.value, num))\n  }\n\n  /**\n   * Create a QuickJS [string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) value.\n   */\n  newString(str: string): QuickJSHandle {\n    const ptr = this.memory\n      .newHeapCharPointer(str)\n      .consume((charHandle) => this.ffi.QTS_NewString(this.ctx.value, charHandle.value))\n    return this.memory.heapValueHandle(ptr)\n  }\n\n  /**\n   * Create a QuickJS [symbol](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol) value.\n   * No two symbols created with this function will be the same value.\n   */\n  newUniqueSymbol(description: string | symbol): QuickJSHandle {\n    const key = (typeof description === \"symbol\" ? description.description : description) ?? \"\"\n    const ptr = this.memory\n      .newHeapCharPointer(key)\n      .consume((charHandle) => this.ffi.QTS_NewSymbol(this.ctx.value, charHandle.value, 0))\n    return this.memory.heapValueHandle(ptr)\n  }\n\n  /**\n   * Get a symbol from the [global registry](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol#shared_symbols_in_the_global_symbol_registry) for the given key.\n   * All symbols created with the same key will be the same value.\n   */\n  newSymbolFor(key: string | symbol): QuickJSHandle {\n    const description = (typeof key === \"symbol\" ? key.description : key) ?? \"\"\n    const ptr = this.memory\n      .newHeapCharPointer(description)\n      .consume((charHandle) => this.ffi.QTS_NewSymbol(this.ctx.value, charHandle.value, 1))\n    return this.memory.heapValueHandle(ptr)\n  }\n\n  /**\n   * Create a QuickJS [bigint](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt) value.\n   */\n  newBigInt(num: bigint): QuickJSHandle {\n    if (!this._BigInt) {\n      const bigIntHandle = this.getProp(this.global, \"BigInt\")\n      this.memory.manage(bigIntHandle)\n      this._BigInt = new StaticLifetime(bigIntHandle.value as JSValueConstPointer, this.runtime)\n    }\n\n    const bigIntHandle = this._BigInt\n    const asString = String(num)\n    return this.newString(asString).consume((handle) =>\n      this.unwrapResult(this.callFunction(bigIntHandle, this.undefined, handle))\n    )\n  }\n\n  /**\n   * `{}`.\n   * Create a new QuickJS [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Object_initializer).\n   *\n   * @param prototype - Like [`Object.create`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/create).\n   */\n  newObject(prototype?: QuickJSHandle): QuickJSHandle {\n    if (prototype) {\n      this.runtime.assertOwned(prototype)\n    }\n    const ptr = prototype\n      ? this.ffi.QTS_NewObjectProto(this.ctx.value, prototype.value)\n      : this.ffi.QTS_NewObject(this.ctx.value)\n    return this.memory.heapValueHandle(ptr)\n  }\n\n  /**\n   * `[]`.\n   * Create a new QuickJS [array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array).\n   */\n  newArray(): QuickJSHandle {\n    const ptr = this.ffi.QTS_NewArray(this.ctx.value)\n    return this.memory.heapValueHandle(ptr)\n  }\n\n  /**\n   * Create a new [[QuickJSDeferredPromise]]. Use `deferred.resolve(handle)` and\n   * `deferred.reject(handle)` to fulfill the promise handle available at `deferred.handle`.\n   * Note that you are responsible for calling `deferred.dispose()` to free the underlying\n   * resources; see the documentation on [[QuickJSDeferredPromise]] for details.\n   */\n  newPromise(): QuickJSDeferredPromise\n  /**\n   * Create a new [[QuickJSDeferredPromise]] that resolves when the\n   * given native Promise<QuickJSHandle> resolves. Rejections will be coerced\n   * to a QuickJS error.\n   *\n   * You can still resolve/reject the created promise \"early\" using its methods.\n   */\n  newPromise(promise: Promise<QuickJSHandle>): QuickJSDeferredPromise\n  /**\n   * Construct a new native Promise<QuickJSHandle>, and then convert it into a\n   * [[QuickJSDeferredPromise]].\n   *\n   * You can still resolve/reject the created promise \"early\" using its methods.\n   */\n  newPromise(\n    newPromiseFn: PromiseExecutor<QuickJSHandle, Error | QuickJSHandle>\n  ): QuickJSDeferredPromise\n  newPromise(\n    value?: PromiseExecutor<QuickJSHandle, Error | QuickJSHandle> | Promise<QuickJSHandle>\n  ): QuickJSDeferredPromise {\n    const deferredPromise = Scope.withScope((scope) => {\n      const mutablePointerArray = scope.manage(\n        this.memory.newMutablePointerArray<JSValuePointerPointer>(2)\n      )\n      const promisePtr = this.ffi.QTS_NewPromiseCapability(\n        this.ctx.value,\n        mutablePointerArray.value.ptr\n      )\n      const promiseHandle = this.memory.heapValueHandle(promisePtr)\n      const [resolveHandle, rejectHandle] = Array.from(mutablePointerArray.value.typedArray).map(\n        (jsvaluePtr) => this.memory.heapValueHandle(jsvaluePtr as any)\n      )\n      return new QuickJSDeferredPromise({\n        context: this,\n        promiseHandle,\n        resolveHandle,\n        rejectHandle,\n      })\n    })\n\n    if (value && typeof value === \"function\") {\n      value = new Promise(value)\n    }\n\n    if (value) {\n      Promise.resolve(value).then(deferredPromise.resolve, (error) =>\n        error instanceof Lifetime\n          ? deferredPromise.reject(error)\n          : this.newError(error).consume(deferredPromise.reject)\n      )\n    }\n\n    return deferredPromise\n  }\n\n  /**\n   * Convert a Javascript function into a QuickJS function value.\n   * See [[VmFunctionImplementation]] for more details.\n   *\n   * A [[VmFunctionImplementation]] should not free its arguments or its return\n   * value. A VmFunctionImplementation should also not retain any references to\n   * its return value.\n   *\n   * To implement an async function, create a promise with [[newPromise]], then\n   * return the deferred promise handle from `deferred.handle` from your\n   * function implementation:\n   *\n   * ```\n   * const deferred = vm.newPromise()\n   * someNativeAsyncFunction().then(deferred.resolve)\n   * return deferred.handle\n   * ```\n   */\n  newFunction(name: string, fn: VmFunctionImplementation<QuickJSHandle>): QuickJSHandle {\n    const fnId = ++this.fnNextId\n    this.setFunction(fnId, fn)\n    return this.memory.heapValueHandle(this.ffi.QTS_NewFunction(this.ctx.value, fnId, name))\n  }\n\n  newError(error: { name: string; message: string }): QuickJSHandle\n  newError(message: string): QuickJSHandle\n  newError(): QuickJSHandle\n  newError(error?: string | { name: string; message: string }): QuickJSHandle {\n    const errorHandle = this.memory.heapValueHandle(this.ffi.QTS_NewError(this.ctx.value))\n\n    if (error && typeof error === \"object\") {\n      if (error.name !== undefined) {\n        this.newString(error.name).consume((handle) => this.setProp(errorHandle, \"name\", handle))\n      }\n\n      if (error.message !== undefined) {\n        this.newString(error.message).consume((handle) =>\n          this.setProp(errorHandle, \"message\", handle)\n        )\n      }\n    } else if (typeof error === \"string\") {\n      this.newString(error).consume((handle) => this.setProp(errorHandle, \"message\", handle))\n    } else if (error !== undefined) {\n      // This isn't supported in the type signature but maybe it will make life easier.\n      this.newString(String(error)).consume((handle) =>\n        this.setProp(errorHandle, \"message\", handle)\n      )\n    }\n\n    return errorHandle\n  }\n\n  // Read values --------------------------------------------------------------\n\n  /**\n   * `typeof` operator. **Not** [standards compliant](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/typeof).\n   *\n   * @remarks\n   * Does not support BigInt values correctly.\n   */\n  typeof(handle: QuickJSHandle) {\n    this.runtime.assertOwned(handle)\n    return this.memory.consumeHeapCharPointer(this.ffi.QTS_Typeof(this.ctx.value, handle.value))\n  }\n\n  /**\n   * Converts `handle` into a Javascript number.\n   * @returns `NaN` on error, otherwise a `number`.\n   */\n  getNumber(handle: QuickJSHandle): number {\n    this.runtime.assertOwned(handle)\n    return this.ffi.QTS_GetFloat64(this.ctx.value, handle.value)\n  }\n\n  /**\n   * Converts `handle` to a Javascript string.\n   */\n  getString(handle: QuickJSHandle): string {\n    this.runtime.assertOwned(handle)\n    return this.memory.consumeJSCharPointer(this.ffi.QTS_GetString(this.ctx.value, handle.value))\n  }\n\n  /**\n   * Converts `handle` into a Javascript symbol. If the symbol is in the global\n   * registry in the guest, it will be created with Symbol.for on the host.\n   */\n  getSymbol(handle: QuickJSHandle): symbol {\n    this.runtime.assertOwned(handle)\n    const key = this.memory.consumeJSCharPointer(\n      this.ffi.QTS_GetSymbolDescriptionOrKey(this.ctx.value, handle.value)\n    )\n    const isGlobal = this.ffi.QTS_IsGlobalSymbol(this.ctx.value, handle.value)\n    return isGlobal ? Symbol.for(key) : Symbol(key)\n  }\n\n  /**\n   * Converts `handle` to a Javascript bigint.\n   */\n  getBigInt(handle: QuickJSHandle): bigint {\n    this.runtime.assertOwned(handle)\n    const asString = this.getString(handle)\n    return BigInt(asString)\n  }\n\n  /**\n   * `Promise.resolve(value)`.\n   * Convert a handle containing a Promise-like value inside the VM into an\n   * actual promise on the host.\n   *\n   * @remarks\n   * You may need to call [[executePendingJobs]] to ensure that the promise is resolved.\n   *\n   * @param promiseLikeHandle - A handle to a Promise-like value with a `.then(onSuccess, onError)` method.\n   */\n  resolvePromise(promiseLikeHandle: QuickJSHandle): Promise<VmCallResult<QuickJSHandle>> {\n    this.runtime.assertOwned(promiseLikeHandle)\n    const vmResolveResult = Scope.withScope((scope) => {\n      const vmPromise = scope.manage(this.getProp(this.global, \"Promise\"))\n      const vmPromiseResolve = scope.manage(this.getProp(vmPromise, \"resolve\"))\n      return this.callFunction(vmPromiseResolve, vmPromise, promiseLikeHandle)\n    })\n    if (vmResolveResult.error) {\n      return Promise.resolve(vmResolveResult)\n    }\n\n    return new Promise<VmCallResult<QuickJSHandle>>((resolve) => {\n      Scope.withScope((scope) => {\n        const resolveHandle = scope.manage(\n          this.newFunction(\"resolve\", (value) => {\n            resolve({ value: value && value.dup() })\n          })\n        )\n\n        const rejectHandle = scope.manage(\n          this.newFunction(\"reject\", (error) => {\n            resolve({ error: error && error.dup() })\n          })\n        )\n\n        const promiseHandle = scope.manage(vmResolveResult.value)\n        const promiseThenHandle = scope.manage(this.getProp(promiseHandle, \"then\"))\n        this.unwrapResult(\n          this.callFunction(promiseThenHandle, promiseHandle, resolveHandle, rejectHandle)\n        ).dispose()\n      })\n    })\n  }\n\n  // Properties ---------------------------------------------------------------\n\n  /**\n   * `handle[key]`.\n   * Get a property from a JSValue.\n   *\n   * @param key - The property may be specified as a JSValue handle, or as a\n   * Javascript string (which will be converted automatically).\n   */\n  getProp(handle: QuickJSHandle, key: QuickJSPropertyKey): QuickJSHandle {\n    this.runtime.assertOwned(handle)\n    const ptr = this.borrowPropertyKey(key).consume((quickJSKey) =>\n      this.ffi.QTS_GetProp(this.ctx.value, handle.value, quickJSKey.value)\n    )\n    const result = this.memory.heapValueHandle(ptr)\n\n    return result\n  }\n\n  /**\n   * `handle[key] = value`.\n   * Set a property on a JSValue.\n   *\n   * @remarks\n   * Note that the QuickJS authors recommend using [[defineProp]] to define new\n   * properties.\n   *\n   * @param key - The property may be specified as a JSValue handle, or as a\n   * Javascript string or number (which will be converted automatically to a JSValue).\n   */\n  setProp(handle: QuickJSHandle, key: QuickJSPropertyKey, value: QuickJSHandle) {\n    this.runtime.assertOwned(handle)\n    // free newly allocated value if key was a string or number. No-op if string was already\n    // a QuickJS handle.\n    this.borrowPropertyKey(key).consume((quickJSKey) =>\n      this.ffi.QTS_SetProp(this.ctx.value, handle.value, quickJSKey.value, value.value)\n    )\n  }\n\n  /**\n   * [`Object.defineProperty(handle, key, descriptor)`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty).\n   *\n   * @param key - The property may be specified as a JSValue handle, or as a\n   * Javascript string or number (which will be converted automatically to a JSValue).\n   */\n  defineProp(\n    handle: QuickJSHandle,\n    key: QuickJSPropertyKey,\n    descriptor: VmPropertyDescriptor<QuickJSHandle>\n  ): void {\n    this.runtime.assertOwned(handle)\n    Scope.withScope((scope) => {\n      const quickJSKey = scope.manage(this.borrowPropertyKey(key))\n\n      const value = descriptor.value || this.undefined\n      const configurable = Boolean(descriptor.configurable)\n      const enumerable = Boolean(descriptor.enumerable)\n      const hasValue = Boolean(descriptor.value)\n      const get = descriptor.get\n        ? scope.manage(this.newFunction(descriptor.get.name, descriptor.get))\n        : this.undefined\n      const set = descriptor.set\n        ? scope.manage(this.newFunction(descriptor.set.name, descriptor.set))\n        : this.undefined\n\n      this.ffi.QTS_DefineProp(\n        this.ctx.value,\n        handle.value,\n        quickJSKey.value,\n        value.value,\n        get.value,\n        set.value,\n        configurable,\n        enumerable,\n        hasValue\n      )\n    })\n  }\n\n  // Evaluation ---------------------------------------------------------------\n\n  /**\n   * [`func.call(thisVal, ...args)`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/call).\n   * Call a JSValue as a function.\n   *\n   * See [[unwrapResult]], which will throw if the function returned an error, or\n   * return the result handle directly. If evaluation returned a handle containing\n   * a promise, use [[resolvePromise]] to convert it to a native promise and\n   * [[executePendingJobs]] to finish evaluating the promise.\n   *\n   * @returns A result. If the function threw synchronously, `result.error` be a\n   * handle to the exception. Otherwise `result.value` will be a handle to the\n   * value.\n   */\n  callFunction(\n    func: QuickJSHandle,\n    thisVal: QuickJSHandle,\n    ...args: QuickJSHandle[]\n  ): VmCallResult<QuickJSHandle> {\n    this.runtime.assertOwned(func)\n    const resultPtr = this.memory\n      .toPointerArray(args)\n      .consume((argsArrayPtr) =>\n        this.ffi.QTS_Call(\n          this.ctx.value,\n          func.value,\n          thisVal.value,\n          args.length,\n          argsArrayPtr.value\n        )\n      )\n\n    const errorPtr = this.ffi.QTS_ResolveException(this.ctx.value, resultPtr)\n    if (errorPtr) {\n      this.ffi.QTS_FreeValuePointer(this.ctx.value, resultPtr)\n      return { error: this.memory.heapValueHandle(errorPtr) }\n    }\n\n    return { value: this.memory.heapValueHandle(resultPtr) }\n  }\n\n  /**\n   * Like [`eval(code)`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/eval#Description).\n   * Evaluates the Javascript source `code` in the global scope of this VM.\n   * When working with async code, you many need to call [[executePendingJobs]]\n   * to execute callbacks pending after synchronous evaluation returns.\n   *\n   * See [[unwrapResult]], which will throw if the function returned an error, or\n   * return the result handle directly. If evaluation returned a handle containing\n   * a promise, use [[resolvePromise]] to convert it to a native promise and\n   * [[executePendingJobs]] to finish evaluating the promise.\n   *\n   * *Note*: to protect against infinite loops, provide an interrupt handler to\n   * [[setInterruptHandler]]. You can use [[shouldInterruptAfterDeadline]] to\n   * create a time-based deadline.\n   *\n   * @returns The last statement's value. If the code threw synchronously,\n   * `result.error` will be a handle to the exception. If execution was\n   * interrupted, the error will have name `InternalError` and message\n   * `interrupted`.\n   */\n  evalCode(\n    code: string,\n    filename: string = \"eval.js\",\n    /**\n     * If no options are passed, a heuristic will be used to detect if `code` is\n     * an ES module.\n     *\n     * See [[EvalFlags]] for number semantics.\n     */\n    options?: number | ContextEvalOptions\n  ): VmCallResult<QuickJSHandle> {\n    const detectModule = (options === undefined ? 1 : 0) as EvalDetectModule\n    const flags = evalOptionsToFlags(options) as EvalFlags\n    const resultPtr = this.memory\n      .newHeapCharPointer(code)\n      .consume((charHandle) =>\n        this.ffi.QTS_Eval(this.ctx.value, charHandle.value, filename, detectModule, flags)\n      )\n    const errorPtr = this.ffi.QTS_ResolveException(this.ctx.value, resultPtr)\n    if (errorPtr) {\n      this.ffi.QTS_FreeValuePointer(this.ctx.value, resultPtr)\n      return { error: this.memory.heapValueHandle(errorPtr) }\n    }\n    return { value: this.memory.heapValueHandle(resultPtr) }\n  }\n\n  /**\n   * Throw an error in the VM, interrupted whatever current execution is in progress when execution resumes.\n   * @experimental\n   */\n  throw(error: Error | QuickJSHandle) {\n    return this.errorToHandle(error).consume((handle) =>\n      this.ffi.QTS_Throw(this.ctx.value, handle.value)\n    )\n  }\n\n  /**\n   * @private\n   */\n  protected borrowPropertyKey(key: QuickJSPropertyKey): QuickJSHandle {\n    if (typeof key === \"number\") {\n      return this.newNumber(key)\n    }\n\n    if (typeof key === \"string\") {\n      return this.newString(key)\n    }\n\n    // key is already a JSValue, but we're borrowing it. Return a static handle\n    // for internal use only.\n    return new StaticLifetime(key.value as JSValueConstPointer, this.runtime)\n  }\n\n  /**\n   * @private\n   */\n  getMemory(rt: JSRuntimePointer): ContextMemory {\n    if (rt === this.rt.value) {\n      return this.memory\n    } else {\n      throw new Error(\"Private API. Cannot get memory from a different runtime\")\n    }\n  }\n\n  // Utilities ----------------------------------------------------------------\n\n  /**\n   * Dump a JSValue to Javascript in a best-effort fashion.\n   * Returns `handle.toString()` if it cannot be serialized to JSON.\n   */\n  dump(handle: QuickJSHandle) {\n    this.runtime.assertOwned(handle)\n    const type = this.typeof(handle)\n    if (type === \"string\") {\n      return this.getString(handle)\n    } else if (type === \"number\") {\n      return this.getNumber(handle)\n    } else if (type === \"bigint\") {\n      return this.getBigInt(handle)\n    } else if (type === \"undefined\") {\n      return undefined\n    } else if (type === \"symbol\") {\n      return this.getSymbol(handle)\n    }\n\n    const str = this.memory.consumeJSCharPointer(this.ffi.QTS_Dump(this.ctx.value, handle.value))\n    try {\n      return JSON.parse(str)\n    } catch (err) {\n      return str\n    }\n  }\n\n  /**\n   * Unwrap a SuccessOrFail result such as a [[VmCallResult]] or a\n   * [[ExecutePendingJobsResult]], where the fail branch contains a handle to a QuickJS error value.\n   * If the result is a success, returns the value.\n   * If the result is an error, converts the error to a native object and throws the error.\n   */\n  unwrapResult<T>(result: SuccessOrFail<T, QuickJSHandle>): T {\n    if (result.error) {\n      const context: QuickJSContext =\n        \"context\" in result.error ? (result.error as { context: QuickJSContext }).context : this\n      const cause = result.error.consume((error) => this.dump(error))\n\n      if (cause && typeof cause === \"object\" && typeof cause.message === \"string\") {\n        const { message, name, stack } = cause\n        const exception = new QuickJSUnwrapError(\"\")\n        const hostStack = exception.stack\n\n        if (typeof name === \"string\") {\n          exception.name = cause.name\n        }\n\n        if (typeof stack === \"string\") {\n          exception.stack = `${name}: ${message}\\n${cause.stack}Host: ${hostStack}`\n        }\n\n        Object.assign(exception, { cause, context, message })\n        throw exception\n      }\n\n      throw new QuickJSUnwrapError(cause, context)\n    }\n\n    return result.value\n  }\n\n  /** @private */\n  protected fnNextId = -32768 // min value of signed 16bit int used by Quickjs\n  /** @private */\n  protected fnMaps = new Map<number, Map<number, VmFunctionImplementation<QuickJSHandle>>>()\n\n  /** @private */\n  protected getFunction(fn_id: number): VmFunctionImplementation<QuickJSHandle> | undefined {\n    const map_id = fn_id >> 8\n    const fnMap = this.fnMaps.get(map_id)\n    if (!fnMap) {\n      return undefined\n    }\n    return fnMap.get(fn_id)\n  }\n\n  /** @private */\n  protected setFunction(fn_id: number, handle: VmFunctionImplementation<QuickJSHandle>) {\n    const map_id = fn_id >> 8\n    let fnMap = this.fnMaps.get(map_id)\n    if (!fnMap) {\n      fnMap = new Map<number, VmFunctionImplementation<QuickJSHandle>>()\n      this.fnMaps.set(map_id, fnMap)\n    }\n    return fnMap.set(fn_id, handle)\n  }\n\n  /**\n   * @hidden\n   */\n  private cToHostCallbacks: ContextCallbacks = {\n    callFunction: (ctx, this_ptr, argc, argv, fn_id) => {\n      if (ctx !== this.ctx.value) {\n        throw new Error(\"QuickJSContext instance received C -> JS call with mismatched ctx\")\n      }\n\n      const fn = this.getFunction(fn_id)\n      if (!fn) {\n        // this \"throw\" is not catch-able from the TS side. could we somehow handle this higher up?\n        throw new Error(`QuickJSContext had no callback with id ${fn_id}`)\n      }\n\n      return Scope.withScopeMaybeAsync(this, function* (awaited, scope) {\n        const thisHandle = scope.manage(\n          new WeakLifetime(this_ptr, this.memory.copyJSValue, this.memory.freeJSValue, this.runtime)\n        )\n        const argHandles = new Array<QuickJSHandle>(argc)\n        for (let i = 0; i < argc; i++) {\n          const ptr = this.ffi.QTS_ArgvGetJSValueConstPointer(argv, i)\n          argHandles[i] = scope.manage(\n            new WeakLifetime(ptr, this.memory.copyJSValue, this.memory.freeJSValue, this.runtime)\n          )\n        }\n\n        try {\n          const result = yield* awaited(fn.apply(thisHandle, argHandles))\n          if (result) {\n            if (\"error\" in result && result.error) {\n              debugLog(\"throw error\", result.error)\n              throw result.error\n            }\n            const handle = scope.manage(result instanceof Lifetime ? result : result.value)\n            return this.ffi.QTS_DupValuePointer(this.ctx.value, handle.value)\n          }\n          return 0 as JSValuePointer\n        } catch (error) {\n          return this.errorToHandle(error as Error).consume((errorHandle) =>\n            this.ffi.QTS_Throw(this.ctx.value, errorHandle.value)\n          )\n        }\n      }) as JSValuePointer\n    },\n  }\n\n  private errorToHandle(error: Error | QuickJSHandle): QuickJSHandle {\n    if (error instanceof Lifetime) {\n      return error\n    }\n\n    return this.newError(error)\n  }\n}\n"]}