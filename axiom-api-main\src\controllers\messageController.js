const axios = require('axios');

// Função genérica para encaminhar requisições para a instância
const forwardToInstance = async (endpoint, body, res) => {
    // A URL base da API interna da instância, vinda do ambiente
    const instanceBaseUrl = process.env.INSTANCE_API_BASE_URL || 'http://api-instance:8080';
    const requestUrl = `${instanceBaseUrl}${endpoint}`;

    try {
        console.log(`[CONTROLLER] Encaminhando requisição para: ${requestUrl}`);
        const response = await axios.post(requestUrl, body);
        res.status(response.status).json(response.data);
    } catch (error) {
        console.error(`[CONTROLLER] Erro ao se comunicar com a instância em ${requestUrl}:`, error.message);
        const status = error.response ? error.response.status : 500;
        const data = error.response ? error.response.data : { status: 'error', message: 'Erro interno no servidor ao se comunicar com a instância.' };
        res.status(status).json(data);
    }
};

const sendTextMessage = (req, res) => {
    forwardToInstance('/send-text', req.body, res);
};

const sendImageMessage = (req, res) => {
    forwardToInstance('/send-image', req.body, res);
};

const sendAudioMessage = (req, res) => {
    forwardToInstance('/send-audio', req.body, res);
};

const sendVideoMessage = (req, res) => {
    forwardToInstance('/send-video', req.body, res);
};

const sendDocumentMessage = (req, res) => {
    forwardToInstance('/send-document', req.body, res);
};



const getConnectionStatus = async (req, res) => {
    const instanceBaseUrl = process.env.INSTANCE_API_BASE_URL || 'http://api-instance:8080';
    const requestUrl = `${instanceBaseUrl}/status`;

    try {
        console.log(`[CONTROLLER] Verificando status da instância em ${requestUrl}`);
        const response = await axios.get(requestUrl);
        res.status(response.status).json(response.data);
    } catch (error) {
        console.error(`[CONTROLLER] Erro ao verificar status da instância em ${requestUrl}:`, error.message);
        const status = error.response ? error.response.status : 500;
        const data = error.response ? error.response.data : { status: 'error', message: 'Erro interno no servidor ao se comunicar com a instância.' };
        res.status(status).json(data);
    }
};

const getQRCode = async (req, res) => {
    const instanceBaseUrl = process.env.INSTANCE_API_BASE_URL || 'http://api-instance:8080';
    const requestUrl = `${instanceBaseUrl}/qr`;

    try {
        console.log(`[CONTROLLER] Solicitando QR Code da instância em ${requestUrl}`);
        const response = await axios.get(requestUrl);
        const qrText = response.data.qr;

        if (!qrText) {
            return res.status(404).json({ message: 'QR Code não disponível no momento.' });
        }

        const QRCode = require('qrcode');
        res.setHeader('Content-Type', 'image/png');
        QRCode.toFileStream(res, qrText, {
            type: 'png',
            errorCorrectionLevel: 'H'
        });

    } catch (error) {
        console.error(`[CONTROLLER] Erro ao solicitar QR Code da instância em ${requestUrl}:`, error.message);
        if (error.response && error.response.status === 404) {
            return res.status(404).json({ message: 'QR Code não disponível no momento.' });
        }
        res.status(500).json({
            status: 'error',
            message: 'Erro interno no servidor ao tentar obter o QR Code.'
        });
    }
};

module.exports = {
    sendTextMessage,
    sendImageMessage,
    sendAudioMessage,
    sendVideoMessage,
    sendDocumentMessage,
    getConnectionStatus,
    getQRCode
};
