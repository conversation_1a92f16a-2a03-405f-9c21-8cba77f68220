{"name": "binary", "version": "0.3.0", "description": "Unpack multibyte binary values from buffers", "main": "./index.js", "repository": {"type": "git", "url": "http://github.com/substack/node-binary.git"}, "keywords": ["binary", "decode", "endian", "unpack", "signed", "unsigned"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {"chainsaw": "~0.1.0", "buffers": "~0.1.1"}, "devDependencies": {"seq": "~0.2.5", "tap": "~0.2.4"}, "scripts": {"test": "tap test/*.js"}, "license": "MIT", "engine": {"node": ">=0.4.0"}}