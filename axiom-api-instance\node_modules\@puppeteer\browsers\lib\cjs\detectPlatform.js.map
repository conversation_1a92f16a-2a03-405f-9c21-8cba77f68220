{"version": 3, "file": "detectPlatform.js", "sourceRoot": "", "sources": ["../../src/detectPlatform.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,4CAAoB;AAEpB,oEAA+D;AAE/D;;GAEG;AACH,SAAgB,qBAAqB;IACnC,MAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,YAAE,CAAC,IAAI,EAAE,KAAK,OAAO;gBAC1B,CAAC,CAAC,iCAAe,CAAC,OAAO;gBACzB,CAAC,CAAC,iCAAe,CAAC,GAAG,CAAC;QAC1B,KAAK,OAAO;YACV,OAAO,iCAAe,CAAC,KAAK,CAAC;QAC/B,KAAK,OAAO;YACV,OAAO,YAAE,CAAC,IAAI,EAAE,KAAK,KAAK;gBACxB,4CAA4C;gBAC5C,CAAC,YAAE,CAAC,IAAI,EAAE,KAAK,OAAO,IAAI,WAAW,CAAC,YAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,iCAAe,CAAC,KAAK;gBACvB,CAAC,CAAC,iCAAe,CAAC,KAAK,CAAC;QAC5B;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAlBD,sDAkBC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,OAAe;IAClC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO,CACL,KAAK,GAAG,EAAE;YACV,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;YAC3B,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,CAChD,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}