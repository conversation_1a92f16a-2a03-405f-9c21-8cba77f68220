// Utilitários para gerenciar localStorage de forma segura

export const STORAGE_KEYS = {
  TOKEN: 'axiom-token',
  USER: 'axiom-user'
} as const;

export const storage = {
  // Salvar token
  setToken: (token: string): boolean => {
    try {
      if (!token || typeof token !== 'string') {
        console.error('[Storage] Token inválido fornecido');
        return false;
      }
      
      localStorage.setItem(STORAGE_KEYS.TOKEN, token);
      console.log('[Storage] Token salvo com sucesso');
      return true;
    } catch (error) {
      console.error('[Storage] Erro ao salvar token:', error);
      return false;
    }
  },

  // Obter token
  getToken: (): string | null => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      console.log('[Storage] Token recuperado:', { hasToken: !!token, length: token?.length || 0 });
      return token;
    } catch (error) {
      console.error('[Storage] Erro ao recuperar token:', error);
      return null;
    }
  },

  // Salvar dados do usuário
  setUser: (user: any): boolean => {
    try {
      if (!user || typeof user !== 'object') {
        console.error('[Storage] Dados de usuário inválidos');
        return false;
      }

      const userString = JSON.stringify(user);
      localStorage.setItem(STORAGE_KEYS.USER, userString);
      console.log('[Storage] Dados do usuário salvos com sucesso');
      return true;
    } catch (error) {
      console.error('[Storage] Erro ao salvar dados do usuário:', error);
      return false;
    }
  },

  // Obter dados do usuário
  getUser: (): any | null => {
    try {
      const userString = localStorage.getItem(STORAGE_KEYS.USER);
      if (!userString) {
        console.log('[Storage] Nenhum dado de usuário encontrado');
        return null;
      }

      const user = JSON.parse(userString);
      console.log('[Storage] Dados do usuário recuperados:', {
        hasUser: !!user,
        userId: user?.id,
        userName: user?.name
      });
      return user;
    } catch (error) {
      console.error('[Storage] Erro ao recuperar dados do usuário:', error);
      // Se houver erro no parse, limpar dados corrompidos
      localStorage.removeItem(STORAGE_KEYS.USER);
      return null;
    }
  },

  // Verificar se está autenticado
  isAuthenticated: (): boolean => {
    const token = storage.getToken();
    const user = storage.getUser();
    const isAuth = !!(token && user && token.length > 10);
    
    console.log('[Storage] Verificação de autenticação:', {
      hasToken: !!token,
      hasUser: !!user,
      tokenLength: token?.length || 0,
      isAuthenticated: isAuth
    });
    
    return isAuth;
  },

  // Limpar todos os dados de autenticação
  clearAuth: (): void => {
    try {
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      console.log('[Storage] Dados de autenticação limpos');
    } catch (error) {
      console.error('[Storage] Erro ao limpar dados de autenticação:', error);
    }
  },

  // Salvar dados de login completos
  setAuthData: (token: string, user: any): boolean => {
    console.log('[Storage] Salvando dados de autenticação completos');
    
    const tokenSaved = storage.setToken(token);
    const userSaved = storage.setUser(user);
    
    const success = tokenSaved && userSaved;
    console.log('[Storage] Resultado do salvamento:', {
      tokenSaved,
      userSaved,
      success
    });
    
    return success;
  },

  // Verificar integridade dos dados
  validateAuthData: (): { isValid: boolean; issues: string[] } => {
    const issues: string[] = [];
    
    const token = storage.getToken();
    const user = storage.getUser();
    
    if (!token) {
      issues.push('Token não encontrado');
    } else if (token.length < 10) {
      issues.push('Token muito curto');
    }
    
    if (!user) {
      issues.push('Dados do usuário não encontrados');
    } else {
      if (!user.id) issues.push('ID do usuário ausente');
      if (!user.email) issues.push('Email do usuário ausente');
      if (!user.name) issues.push('Nome do usuário ausente');
    }
    
    const isValid = issues.length === 0;
    
    console.log('[Storage] Validação dos dados de autenticação:', {
      isValid,
      issues
    });
    
    return { isValid, issues };
  }
};

export default storage;
