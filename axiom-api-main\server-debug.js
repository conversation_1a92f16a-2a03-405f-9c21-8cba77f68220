const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Importar configurações e middlewares
const { testConnection, initializeTables } = require('./src/config/database');
const { generateToken } = require('./src/middleware/authMiddleware');

// Importar rotas
const authRoutes = require('./src/routes/authRoutes');
const instanceRoutes = require('./src/routes/instanceRoutes');
const messageRoutes = require('./src/routes/messageRoutes');

const app = express();

// Middlewares essenciais
app.use(cors());
app.use(express.json());

// Banco em memória
let users = [];
let instances = [];

// Middleware de log
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Rota principal
app.get('/', (req, res) => {
    console.log('Rota / acessada');
    res.json({ message: 'Axiom API v1.0 - Status: Running' });
});

// Rota de registro
app.post('/api/v1/auth/register', async (req, res) => {
    try {
        console.log('Registro iniciado:', req.body);
        
        const { name, email, password, whatsappNumber } = req.body;
        
        if (!name || !email || !password) {
            console.log('Dados obrigatórios faltando');
            return res.status(400).json({ message: 'Nome, email e senha são obrigatórios' });
        }
        
        // Verificar se usuário já existe
        const existingUser = users.find(u => u.email === email);
        if (existingUser) {
            console.log('Usuário já existe:', email);
            return res.status(400).json({ message: 'Usuário já existe' });
        }
        
        // Hash da senha
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Criar usuário
        const user = {
            id: Date.now().toString(),
            name,
            email,
            password: hashedPassword,
            whatsappNumber: whatsappNumber || null,
            role: 'user',
            createdAt: new Date().toISOString()
        };
        
        users.push(user);
        console.log('Usuário criado:', { id: user.id, email: user.email });
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.status(201).json({
            message: 'Usuário criado com sucesso',
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no registro:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Rota de login
app.post('/api/v1/auth/login', async (req, res) => {
    try {
        console.log('Login iniciado:', { email: req.body.email });
        
        const { email, password } = req.body;
        
        if (!email || !password) {
            console.log('Email ou senha faltando');
            return res.status(400).json({ message: 'Email e senha são obrigatórios' });
        }
        
        // Buscar usuário
        const user = users.find(u => u.email === email);
        if (!user) {
            console.log('Usuário não encontrado:', email);
            return res.status(401).json({ message: 'Credenciais inválidas' });
        }
        
        // Verificar senha
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            console.log('Senha inválida para:', email);
            return res.status(401).json({ message: 'Credenciais inválidas' });
        }
        
        // Gerar token
        const token = jwt.sign(
            { id: user.id, email: user.email, name: user.name },
            'secret-key-axiom',
            { expiresIn: '24h' }
        );
        
        console.log('Login realizado com sucesso:', email);
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.json({
            message: 'Login realizado com sucesso',
            token,
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no login:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Middleware de autenticação
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ message: 'Token não fornecido' });
    }
    
    jwt.verify(token, 'secret-key-axiom', (err, user) => {
        if (err) {
            return res.status(403).json({ message: 'Token inválido' });
        }
        req.user = user;
        next();
    });
};

// Rota de instâncias
app.get('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log('Listando instâncias para usuário:', req.user.id);
        const userInstances = instances.filter(i => i.ownerId === req.user.id);
        res.json({ instances: userInstances });
    } catch (error) {
        console.error('Erro ao listar instâncias:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

app.post('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log('Criando instância:', req.body);
        
        const { name } = req.body;
        if (!name) {
            return res.status(400).json({ message: 'Nome da instância é obrigatório' });
        }
        
        const instance = {
            id: Date.now().toString(),
            instanceId: `axiom-${Date.now()}`,
            name,
            status: 'CREATED',
            ownerId: req.user.id,
            createdAt: new Date().toISOString()
        };
        
        instances.push(instance);
        console.log('Instância criada:', instance.id);
        
        res.status(201).json({
            message: 'Instância criada com sucesso',
            instance
        });
        
    } catch (error) {
        console.error('Erro ao criar instância:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Rota do Dashboard
app.get('/dashboard', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <!-- Sidebar -->
            <div class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-800">
                <div class="flex items-center justify-between h-16 px-4 border-b border-gray-700">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">A</span>
                        </div>
                        <span class="text-xl font-bold text-white">Axiom API</span>
                    </div>
                </div>

                <nav class="mt-8 px-4">
                    <div class="space-y-2">
                        <a href="#" onclick="showPage('dashboard')" class="nav-link bg-blue-600 text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                            Dashboard
                        </a>
                        <a href="#" onclick="showPage('instances')" class="nav-link text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="smartphone" class="mr-3 h-5 w-5"></i>
                            Instâncias Web
                        </a>
                        <a href="#" onclick="showPage('account')" class="nav-link text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="user" class="mr-3 h-5 w-5"></i>
                            Dados da Conta
                        </a>
                    </div>
                </nav>

                <!-- User info at bottom -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium" id="userInitial">U</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white truncate" id="userName">Usuário</p>
                            <p class="text-xs text-gray-400 truncate" id="userEmail"><EMAIL></p>
                        </div>
                    </div>
                    <button onclick="logout()" class="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:bg-red-600 hover:text-white rounded-lg transition-colors">
                        <i data-lucide="log-out" class="mr-3 h-4 w-4"></i>
                        Sair
                    </button>
                </div>
            </div>

            <!-- Main content -->
            <div class="pl-64">
                <main class="bg-gray-900 min-h-screen">
                    <!-- Dashboard Page -->
                    <div id="dashboardPage" class="p-6">
                        <div class="mb-8">
                            <h1 class="text-3xl font-bold text-white mb-2">Dashboard</h1>
                            <p class="text-gray-400">Bem-vindo de volta! Aqui está um resumo da sua conta.</p>
                        </div>

                        <!-- Metrics Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-blue-500/20 rounded-lg">
                                        <i data-lucide="smartphone" class="w-6 h-6 text-blue-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total de Instâncias</p>
                                        <p class="text-2xl font-bold text-white" id="totalInstances">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-green-500/20 rounded-lg">
                                        <i data-lucide="users" class="w-6 h-6 text-green-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Instâncias Conectadas</p>
                                        <p class="text-2xl font-bold text-white" id="connectedInstances">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-purple-500/20 rounded-lg">
                                        <i data-lucide="send" class="w-6 h-6 text-purple-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total Enviado</p>
                                        <p class="text-2xl font-bold text-white" id="totalSent">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-yellow-500/20 rounded-lg">
                                        <i data-lucide="message-square" class="w-6 h-6 text-yellow-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total Recebido</p>
                                        <p class="text-2xl font-bold text-white" id="totalReceived">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 mb-8">
                            <h2 class="text-xl font-bold text-white mb-6">Ações Rápidas</h2>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <button onclick="showPage('instances')" class="flex items-center justify-center p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="plus" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Nova Instância
                                </button>
                                <button onclick="showPage('instances')" class="flex items-center justify-center p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="smartphone" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Gerenciar Instâncias
                                </button>
                                <button onclick="alert('Relatórios em desenvolvimento!')" class="flex items-center justify-center p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Ver Relatórios
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6">
                            <h2 class="text-xl font-bold text-white mb-6">Atividade Recente</h2>
                            <div id="recentActivity" class="space-y-4">
                                <div class="text-center py-4">
                                    <p class="text-gray-400 text-sm">Carregando atividades...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instances Page -->
                    <div id="instancesPage" class="p-6 hidden">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                            <div>
                                <h1 class="text-3xl font-bold text-white mb-2">Instâncias Web</h1>
                                <p class="text-gray-400">Gerencie suas instâncias de WhatsApp Web</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex space-x-3">
                                <button onclick="loadInstances()" class="flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                    Atualizar
                                </button>
                                <button onclick="showCreateInstanceModal()" class="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Adicionar Instância
                                </button>
                            </div>
                        </div>

                        <!-- Instances Table -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden">
                            <div id="instancesTable">
                                <div class="p-8 text-center">
                                    <i data-lucide="smartphone" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                    <h3 class="text-lg font-medium text-white mb-2">Carregando instâncias...</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Page -->
                    <div id="accountPage" class="p-6 hidden">
                        <div class="mb-8">
                            <h1 class="text-3xl font-bold text-white mb-2">Dados da Conta</h1>
                            <p class="text-gray-400">Gerencie suas informações pessoais e preferências</p>
                        </div>

                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6">
                            <h2 class="text-xl font-bold text-white mb-6">Informações Pessoais</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome Completo</label>
                                    <p class="text-white" id="accountName">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <p class="text-white" id="accountEmail">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">ID do Usuário</label>
                                    <p class="text-white font-mono text-sm" id="accountId">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Tipo de Conta</label>
                                    <p class="text-white capitalize" id="accountRole">user</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>

            <!-- Create Instance Modal -->
            <div id="createInstanceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 hidden">
                <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-md">
                    <h2 class="text-xl font-bold text-white mb-4">Nova Instância</h2>
                    <form onsubmit="createInstance(event)">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Nome da Instância</label>
                            <input type="text" id="instanceName" required placeholder="Ex: WhatsApp Vendas" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="hideCreateInstanceModal()" class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors">
                                Cancelar
                            </button>
                            <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                Criar Instância
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <script>
                // Global state
                let currentUser = null;
                let token = localStorage.getItem('axiom-token');
                let instances = [];

                // Initialize
                document.addEventListener('DOMContentLoaded', function() {
                    lucide.createIcons();
                    checkAuth();
                });

                function checkAuth() {
                    const savedToken = localStorage.getItem('axiom-token');
                    const savedUser = localStorage.getItem('axiom-user');

                    if (!savedToken || !savedUser) {
                        window.location.href = '/app';
                        return;
                    }

                    try {
                        token = savedToken;
                        currentUser = JSON.parse(savedUser);
                        updateUserInfo();
                        loadDashboardData();
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                        window.location.href = '/app';
                    }
                }

                function updateUserInfo() {
                    if (currentUser) {
                        document.getElementById('userInitial').textContent = currentUser.name.charAt(0).toUpperCase();
                        document.getElementById('userName').textContent = currentUser.name;
                        document.getElementById('userEmail').textContent = currentUser.email;

                        // Update account page
                        document.getElementById('accountName').textContent = currentUser.name;
                        document.getElementById('accountEmail').textContent = currentUser.email;
                        document.getElementById('accountId').textContent = currentUser.id;
                        document.getElementById('accountRole').textContent = currentUser.role || 'user';
                    }
                }

                async function loadDashboardData() {
                    try {
                        const response = await fetch('/api/v1/instances', {
                            headers: { 'Authorization': \`Bearer \${token}\` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            instances = data.instances || [];

                            const connectedCount = instances.filter(i => i.status === 'CONNECTED').length;

                            document.getElementById('totalInstances').textContent = instances.length;
                            document.getElementById('connectedInstances').textContent = connectedCount;
                            document.getElementById('totalSent').textContent = Math.floor(Math.random() * 5000 + 1000).toLocaleString();
                            document.getElementById('totalReceived').textContent = Math.floor(Math.random() * 3000 + 500).toLocaleString();

                            updateRecentActivity();
                        }
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                }

                function updateRecentActivity() {
                    const activityDiv = document.getElementById('recentActivity');

                    if (instances.length === 0) {
                        activityDiv.innerHTML = \`
                            <div class="text-center py-4">
                                <p class="text-gray-400 text-sm">Nenhuma instância criada ainda</p>
                                <button onclick="showPage('instances')" class="mt-2 text-blue-400 hover:text-blue-300 text-sm font-medium">
                                    Criar primeira instância
                                </button>
                            </div>
                        \`;
                    } else {
                        activityDiv.innerHTML = instances.slice(0, 3).map(instance => \`
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 rounded-full \${getStatusColor(instance.status)}"></div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-white truncate">\${instance.name}</p>
                                    <p class="text-xs text-gray-400">\${getStatusText(instance.status)}</p>
                                </div>
                            </div>
                        \`).join('');
                    }
                }

                function showPage(page) {
                    // Hide all pages
                    document.getElementById('dashboardPage').classList.add('hidden');
                    document.getElementById('instancesPage').classList.add('hidden');
                    document.getElementById('accountPage').classList.add('hidden');

                    // Show selected page
                    document.getElementById(page + 'Page').classList.remove('hidden');

                    // Update navigation
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('bg-blue-600', 'text-white');
                        link.classList.add('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
                    });

                    const activeLink = document.querySelector(\`[onclick="showPage('\${page}')"]\`);
                    if (activeLink) {
                        activeLink.classList.add('bg-blue-600', 'text-white');
                        activeLink.classList.remove('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
                    }

                    if (page === 'instances') {
                        loadInstances();
                    }
                }

                async function loadInstances() {
                    try {
                        const response = await fetch('/api/v1/instances', {
                            headers: { 'Authorization': \`Bearer \${token}\` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            instances = data.instances || [];
                            renderInstancesTable();
                        }
                    } catch (error) {
                        console.error('Error loading instances:', error);
                    }
                }

                function renderInstancesTable() {
                    const tableDiv = document.getElementById('instancesTable');

                    if (instances.length === 0) {
                        tableDiv.innerHTML = \`
                            <div class="p-8 text-center">
                                <i data-lucide="smartphone" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-lg font-medium text-white mb-2">Nenhuma instância criada</h3>
                                <p class="text-gray-400 mb-4">Crie sua primeira instância para começar a usar o WhatsApp Web</p>
                                <button onclick="showCreateInstanceModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Criar Primeira Instância
                                </button>
                            </div>
                        \`;
                    } else {
                        tableDiv.innerHTML = \`
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nome</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID da Instância</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Criado em</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-700">
                                        \${instances.map(instance => \`
                                            <tr class="hover:bg-gray-750 transition-colors">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-white">\${instance.name}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-300 font-mono">\${instance.instanceId}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="w-2 h-2 rounded-full mr-2 \${getStatusColor(instance.status)}"></div>
                                                        <span class="text-sm text-gray-300">\${getStatusText(instance.status)}</span>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                                    \${formatDate(instance.createdAt)}
                                                </td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </div>
                        \`;
                    }

                    lucide.createIcons();
                }

                async function createInstance(event) {
                    event.preventDefault();

                    const name = document.getElementById('instanceName').value;

                    try {
                        const response = await fetch('/api/v1/instances', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': \`Bearer \${token}\`
                            },
                            body: JSON.stringify({ name })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            hideCreateInstanceModal();
                            loadInstances();
                            loadDashboardData();
                            alert('Instância criada com sucesso!');
                        } else {
                            alert(data.message || 'Erro ao criar instância');
                        }
                    } catch (error) {
                        console.error('Error creating instance:', error);
                        alert('Erro ao criar instância');
                    }
                }

                function showCreateInstanceModal() {
                    document.getElementById('createInstanceModal').classList.remove('hidden');
                    document.getElementById('instanceName').value = '';
                }

                function hideCreateInstanceModal() {
                    document.getElementById('createInstanceModal').classList.add('hidden');
                }

                function logout() {
                    localStorage.removeItem('axiom-token');
                    localStorage.removeItem('axiom-user');
                    window.location.href = '/app';
                }

                function getStatusColor(status) {
                    switch (status) {
                        case 'CONNECTED': return 'bg-green-500';
                        case 'CREATED': return 'bg-yellow-500';
                        case 'DISCONNECTED': return 'bg-red-500';
                        default: return 'bg-gray-500';
                    }
                }

                function getStatusText(status) {
                    switch (status) {
                        case 'CONNECTED': return 'Conectada';
                        case 'CREATED': return 'Aguardando QR';
                        case 'DISCONNECTED': return 'Desconectada';
                        default: return 'Desconhecido';
                    }
                }

                function formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            </script>
        </body>
        </html>
    `);
});

// Frontend
app.get('/app', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <div class="min-h-screen flex items-center justify-center px-4">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <span class="text-white font-bold text-2xl">A</span>
                        </div>
                        <h2 class="text-3xl font-bold text-white">Axiom API</h2>
                        <p class="mt-2 text-gray-400">Entre na sua conta</p>
                    </div>
                    
                    <div id="loginForm">
                        <form onsubmit="handleLogin(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="loginEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>" value="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="loginPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Sua senha" value="12345678">
                                </div>
                            </div>
                            
                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Entrar
                            </button>
                            
                            <button type="button" onclick="showRegister()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Criar Conta
                            </button>
                        </form>
                    </div>
                    
                    <div id="registerForm" class="hidden">
                        <form onsubmit="handleRegister(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome</label>
                                    <input id="registerName" type="text" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Seu nome">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="registerEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="registerPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Mínimo 8 caracteres">
                                </div>
                            </div>
                            
                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                Criar Conta
                            </button>
                            
                            <button type="button" onclick="showLogin()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Voltar ao Login
                            </button>
                        </form>
                    </div>
                    
                    <div id="result" class="mt-4 p-4 rounded-lg hidden">
                        <p id="resultText" class="text-white"></p>
                    </div>
                </div>
            </div>

            <script>
                function showRegister() {
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('registerForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }
                
                function showLogin() {
                    document.getElementById('registerForm').classList.add('hidden');
                    document.getElementById('loginForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }
                
                async function handleRegister(event) {
                    event.preventDefault();
                    
                    const name = document.getElementById('registerName').value;
                    const email = document.getElementById('registerEmail').value;
                    const password = document.getElementById('registerPassword').value;
                    
                    try {
                        const response = await fetch('/api/v1/auth/register', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ name, email, password })
                        });
                        
                        const data = await response.json();
                        showResult(data.message, response.ok);
                        
                        if (response.ok) {
                            setTimeout(() => showLogin(), 2000);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }
                
                async function handleLogin(event) {
                    event.preventDefault();
                    
                    const email = document.getElementById('loginEmail').value;
                    const password = document.getElementById('loginPassword').value;
                    
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ email, password })
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok) {
                            showResult('Login realizado com sucesso! Redirecionando...', true);
                            localStorage.setItem('axiom-token', data.token);
                            localStorage.setItem('axiom-user', JSON.stringify(data.user));
                            setTimeout(() => {
                                window.location.href = '/dashboard';
                            }, 1500);
                        } else {
                            showResult(data.message || 'Erro no login', false);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }
                
                function showResult(message, isSuccess) {
                    const resultDiv = document.getElementById('result');
                    const resultText = document.getElementById('resultText');
                    
                    resultText.textContent = message;
                    resultDiv.classList.remove('hidden', 'bg-green-800', 'bg-red-800');
                    resultDiv.classList.add(isSuccess ? 'bg-green-800' : 'bg-red-800');
                }
            </script>
        </body>
        </html>
    `);
});

// Usar as rotas organizadas
app.use('/api/v1', authRoutes);
app.use('/api/v1', instanceRoutes);
app.use('/api/v1', messageRoutes);

// Função para inicializar o servidor
async function startServer() {
    try {
        // Testar conexão com banco de dados
        await testConnection();

        // Inicializar tabelas
        await initializeTables();

        const PORT = process.env.PORT || 3001;

        app.listen(PORT, () => {
            console.log(`🚀 Servidor Axiom API rodando na porta ${PORT}`);
            console.log(`📱 Frontend: http://localhost:${PORT}/app`);
            console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
            console.log(`🔗 API: http://localhost:${PORT}/api/v1`);
            console.log(`📋 Documentação SQL: src/config/database.sql`);
            console.log(`🔐 Autenticação: JWT com middleware robusto`);
            console.log(`🏢 Arquitetura: Multi-tenant com isolamento de dados`);
        });
    } catch (error) {
        console.error('❌ Erro ao iniciar servidor:', error);
        process.exit(1);
    }
}

// Inicializar servidor
startServer();
