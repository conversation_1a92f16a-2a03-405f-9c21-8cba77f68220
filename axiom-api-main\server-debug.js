const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();

// Middlewares essenciais
app.use(cors());
app.use(express.json());

// Banco em memória
let users = [];
let instances = [];

// Middleware de log
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Rota principal
app.get('/', (req, res) => {
    console.log('Rota / acessada');
    res.json({ message: 'Axiom API v1.0 - Status: Running' });
});

// Rota de registro
app.post('/api/v1/auth/register', async (req, res) => {
    try {
        console.log('Registro iniciado:', req.body);
        
        const { name, email, password, whatsappNumber } = req.body;
        
        if (!name || !email || !password) {
            console.log('Dados obrigatórios faltando');
            return res.status(400).json({ message: 'Nome, email e senha são obrigatórios' });
        }
        
        // Verificar se usuário já existe
        const existingUser = users.find(u => u.email === email);
        if (existingUser) {
            console.log('Usuário já existe:', email);
            return res.status(400).json({ message: 'Usuário já existe' });
        }
        
        // Hash da senha
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Criar usuário
        const user = {
            id: Date.now().toString(),
            name,
            email,
            password: hashedPassword,
            whatsappNumber: whatsappNumber || null,
            role: 'user',
            createdAt: new Date().toISOString()
        };
        
        users.push(user);
        console.log('Usuário criado:', { id: user.id, email: user.email });
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.status(201).json({
            message: 'Usuário criado com sucesso',
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no registro:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Rota de login
app.post('/api/v1/auth/login', async (req, res) => {
    try {
        console.log('Login iniciado:', { email: req.body.email });
        
        const { email, password } = req.body;
        
        if (!email || !password) {
            console.log('Email ou senha faltando');
            return res.status(400).json({ message: 'Email e senha são obrigatórios' });
        }
        
        // Buscar usuário
        const user = users.find(u => u.email === email);
        if (!user) {
            console.log('Usuário não encontrado:', email);
            return res.status(401).json({ message: 'Credenciais inválidas' });
        }
        
        // Verificar senha
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            console.log('Senha inválida para:', email);
            return res.status(401).json({ message: 'Credenciais inválidas' });
        }
        
        // Gerar token
        const token = jwt.sign(
            { id: user.id, email: user.email, name: user.name },
            'secret-key-axiom',
            { expiresIn: '24h' }
        );
        
        console.log('Login realizado com sucesso:', email);
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.json({
            message: 'Login realizado com sucesso',
            token,
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no login:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Middleware de autenticação
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ message: 'Token não fornecido' });
    }
    
    jwt.verify(token, 'secret-key-axiom', (err, user) => {
        if (err) {
            return res.status(403).json({ message: 'Token inválido' });
        }
        req.user = user;
        next();
    });
};

// Rota de instâncias
app.get('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log('Listando instâncias para usuário:', req.user.id);
        const userInstances = instances.filter(i => i.ownerId === req.user.id);
        res.json({ instances: userInstances });
    } catch (error) {
        console.error('Erro ao listar instâncias:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

app.post('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log('Criando instância:', req.body);
        
        const { name } = req.body;
        if (!name) {
            return res.status(400).json({ message: 'Nome da instância é obrigatório' });
        }
        
        const instance = {
            id: Date.now().toString(),
            instanceId: `axiom-${Date.now()}`,
            name,
            status: 'CREATED',
            ownerId: req.user.id,
            createdAt: new Date().toISOString()
        };
        
        instances.push(instance);
        console.log('Instância criada:', instance.id);
        
        res.status(201).json({
            message: 'Instância criada com sucesso',
            instance
        });
        
    } catch (error) {
        console.error('Erro ao criar instância:', error);
        res.status(500).json({ message: 'Erro interno do servidor' });
    }
});

// Frontend
app.get('/app', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <div class="min-h-screen flex items-center justify-center px-4">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <span class="text-white font-bold text-2xl">A</span>
                        </div>
                        <h2 class="text-3xl font-bold text-white">Axiom API</h2>
                        <p class="mt-2 text-gray-400">Entre na sua conta</p>
                    </div>
                    
                    <div id="loginForm">
                        <form onsubmit="handleLogin(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="loginEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>" value="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="loginPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Sua senha" value="12345678">
                                </div>
                            </div>
                            
                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Entrar
                            </button>
                            
                            <button type="button" onclick="showRegister()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Criar Conta
                            </button>
                        </form>
                    </div>
                    
                    <div id="registerForm" class="hidden">
                        <form onsubmit="handleRegister(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome</label>
                                    <input id="registerName" type="text" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Seu nome">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="registerEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="registerPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Mínimo 8 caracteres">
                                </div>
                            </div>
                            
                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                Criar Conta
                            </button>
                            
                            <button type="button" onclick="showLogin()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Voltar ao Login
                            </button>
                        </form>
                    </div>
                    
                    <div id="result" class="mt-4 p-4 rounded-lg hidden">
                        <p id="resultText" class="text-white"></p>
                    </div>
                </div>
            </div>

            <script>
                function showRegister() {
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('registerForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }
                
                function showLogin() {
                    document.getElementById('registerForm').classList.add('hidden');
                    document.getElementById('loginForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }
                
                async function handleRegister(event) {
                    event.preventDefault();
                    
                    const name = document.getElementById('registerName').value;
                    const email = document.getElementById('registerEmail').value;
                    const password = document.getElementById('registerPassword').value;
                    
                    try {
                        const response = await fetch('/api/v1/auth/register', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ name, email, password })
                        });
                        
                        const data = await response.json();
                        showResult(data.message, response.ok);
                        
                        if (response.ok) {
                            setTimeout(() => showLogin(), 2000);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }
                
                async function handleLogin(event) {
                    event.preventDefault();
                    
                    const email = document.getElementById('loginEmail').value;
                    const password = document.getElementById('loginPassword').value;
                    
                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ email, password })
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok) {
                            showResult('Login realizado com sucesso! Redirecionando...', true);
                            localStorage.setItem('axiom-token', data.token);
                            localStorage.setItem('axiom-user', JSON.stringify(data.user));
                            setTimeout(() => {
                                window.location.href = '/dashboard';
                            }, 1500);
                        } else {
                            showResult(data.message || 'Erro no login', false);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }
                
                function showResult(message, isSuccess) {
                    const resultDiv = document.getElementById('result');
                    const resultText = document.getElementById('resultText');
                    
                    resultText.textContent = message;
                    resultDiv.classList.remove('hidden', 'bg-green-800', 'bg-red-800');
                    resultDiv.classList.add(isSuccess ? 'bg-green-800' : 'bg-red-800');
                }
            </script>
        </body>
        </html>
    `);
});

// Inicializar servidor
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`🚀 Servidor rodando na porta ${PORT}`);
    console.log(`📱 Frontend: http://localhost:${PORT}/app`);
    console.log(`🔗 API: http://localhost:${PORT}/api/v1`);
    console.log(`📊 Usuários: ${users.length}`);
    console.log(`📱 Instâncias: ${instances.length}`);
});
