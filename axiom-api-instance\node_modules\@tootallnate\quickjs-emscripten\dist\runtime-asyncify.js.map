{"version": 3, "file": "runtime-asyncify.js", "sourceRoot": "", "sources": ["../ts/runtime-asyncify.ts"], "names": [], "mappings": ";;;AACA,wBAA4B;AAC5B,yDAAwD;AAKxD,uCAA0C;AAC1C,mCAOgB;AAEhB,MAAa,mBAAoB,SAAQ,wBAAc;IAcrD,eAAe;IACf,YAAY,IAKX;QACC,KAAK,CAAC,IAAI,CAAC,CAAA;IACb,CAAC;IAEQ,UAAU,CAAC,UAA0B,EAAE;QAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,KAAK,yBAAiB,EAAE;YAClE,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAA;SACjE;QAED,MAAM,GAAG,GAAG,IAAI,WAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACtF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC/B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;YACrC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;QACnC,CAAC,CAAC,CAAA;QAEF,MAAM,OAAO,GAAG,IAAI,sCAAmB,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG;YACH,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,cAAc,EAAE,EAAE;YAClB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CAAA;QACF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAEvC,OAAO,OAAO,CAAA;IAChB,CAAC;IAEe,eAAe,CAC7B,YAAiC,EACjC,gBAA0C;QAE1C,KAAK,CAAC,eAAe,CACnB,YAA8B,EAC9B,gBAAkD,CACnD,CAAA;IACH,CAAC;IAED;;;;;;OAMG;IACa,eAAe,CAAC,SAAiB;QAC/C,OAAO,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;IACzC,CAAC;CACF;AArED,kDAqEC", "sourcesContent": ["import type { QuickJSAsyncWASMModule } from \"./module-asyncify\"\nimport { Lifetime } from \".\"\nimport { QuickJSAsyncContext } from \"./context-asyncify\"\nimport { QuickJSAsyncEmscriptenModule } from \"./emscripten-types\"\nimport { QuickJSAsyncFFI } from \"./variants\"\nimport { <PERSON><PERSON><PERSON>xtPointer, J<PERSON>untimePointer } from \"./types-ffi\"\nimport { QuickJSModuleCallbacks } from \"./module\"\nimport { QuickJSRuntime } from \"./runtime\"\nimport {\n  ContextOptions,\n  DefaultIntrinsics,\n  JSModuleLoader,\n  JSModuleLoaderAsync,\n  JSModuleNormalizer,\n  JSModuleNormalizerAsync,\n} from \"./types\"\n\nexport class QuickJSAsyncRuntime extends QuickJSRuntime {\n  public context: QuickJSAsyncContext | undefined\n\n  /** @private */\n  protected declare module: QuickJSAsyncEmscriptenModule\n  /** @private */\n  protected declare ffi: QuickJSAsyncFFI\n  /** @private */\n  protected declare rt: Lifetime<JSRuntimePointer>\n  /** @private */\n  protected declare callbacks: QuickJSModuleCallbacks\n  /** @private */\n  protected declare contextMap: Map<JSContextPointer, QuickJSAsyncContext>\n\n  /** @private */\n  constructor(args: {\n    module: QuickJSAsyncEmscriptenModule\n    ffi: QuickJSAsyncFFI\n    rt: Lifetime<JSRuntimePointer>\n    callbacks: QuickJSModuleCallbacks\n  }) {\n    super(args)\n  }\n\n  override newContext(options: ContextOptions = {}): QuickJSAsyncContext {\n    if (options.intrinsics && options.intrinsics !== DefaultIntrinsics) {\n      throw new Error(\"TODO: Custom intrinsics are not supported yet\")\n    }\n\n    const ctx = new Lifetime(this.ffi.QTS_NewContext(this.rt.value), undefined, (ctx_ptr) => {\n      this.contextMap.delete(ctx_ptr)\n      this.callbacks.deleteContext(ctx_ptr)\n      this.ffi.QTS_FreeContext(ctx_ptr)\n    })\n\n    const context = new QuickJSAsyncContext({\n      module: this.module,\n      ctx,\n      ffi: this.ffi,\n      rt: this.rt,\n      ownedLifetimes: [],\n      runtime: this,\n      callbacks: this.callbacks,\n    })\n    this.contextMap.set(ctx.value, context)\n\n    return context\n  }\n\n  public override setModuleLoader(\n    moduleLoader: JSModuleLoaderAsync,\n    moduleNormalizer?: JSModuleNormalizerAsync\n  ): void {\n    super.setModuleLoader(\n      moduleLoader as JSModuleLoader,\n      moduleNormalizer as JSModuleNormalizer | undefined\n    )\n  }\n\n  /**\n   * Set the max stack size for this runtime in bytes.\n   * To remove the limit, set to `0`.\n   *\n   * Setting this limit also adjusts the global `ASYNCIFY_STACK_SIZE` for the entire {@link QuickJSAsyncWASMModule}.\n   * See the [pull request](https://github.com/justjake/quickjs-emscripten/pull/114) for more details.\n   */\n  public override setMaxStackSize(stackSize: number): void {\n    return super.setMaxStackSize(stackSize)\n  }\n}\n"]}