{"version": 3, "names": ["_is", "require", "_isValidIdentifier", "_helperValidatorIdentifier", "_helper<PERSON><PERSON><PERSON><PERSON>er", "_index", "_utils", "defineType", "defineAliasedType", "fields", "elements", "validate", "arrayOf", "assertNodeOrValueType", "default", "process", "env", "BABEL_TYPES_8_BREAKING", "undefined", "visitor", "aliases", "operator", "assertValueType", "Object", "assign", "identifier", "assertOneOf", "ASSIGNMENT_OPERATORS", "pattern", "node", "key", "val", "validator", "is", "left", "oneOf", "assertNodeType", "right", "builder", "BINARY_OPERATORS", "expression", "inOp", "oneOfNodeTypes", "value", "directives", "arrayOfType", "body", "validateArrayOfType", "label", "optional", "callee", "arguments", "typeArguments", "typeParameters", "param", "test", "consequent", "alternate", "program", "comments", "each", "assertEach", "tokens", "type", "init", "update", "functionCommon", "params", "generator", "async", "exports", "functionTypeAnnotationCommon", "returnType", "functionDeclaration<PERSON>ommon", "declare", "id", "predicate", "parent", "inherits", "patternLikeCommon", "typeAnnotation", "decorators", "name", "chain", "isValidIdentifier", "TypeError", "match", "exec", "toString", "parent<PERSON><PERSON>", "nonComp", "computed", "imported", "meta", "isKeyword", "isReservedWord", "depre<PERSON><PERSON><PERSON><PERSON>", "Number", "isFinite", "error", "Error", "flags", "invalid", "LOGICAL_OPERATORS", "object", "property", "normal", "sourceType", "interpreter", "properties", "kind", "shorthand", "argument", "<PERSON><PERSON><PERSON>", "index", "length", "expressions", "discriminant", "cases", "block", "handler", "finalizer", "prefix", "UNARY_OPERATORS", "UPDATE_OPERATORS", "declarations", "withoutInit", "constOrLetOrVar", "usingOrAwaitUsing", "parentIsForX", "decl", "definite", "superClass", "implements", "mixins", "abstract", "importAttributes", "attributes", "assertions", "deprecated", "source", "exportKind", "validateOptional", "declaration", "validateType", "specifiers", "sourced", "sourceless", "local", "exported", "lval", "await", "module", "phase", "importKind", "options", "classMethodOrPropertyCommon", "accessibility", "static", "override", "classMethodOrDeclareMethodCommon", "access", "tag", "quasi", "assertShape", "raw", "cooked", "templateElementCookedValidator", "unterminatedCalled", "str", "firstInvalidLoc", "readStringContents", "unterminated", "strictNumericEscape", "invalidEscapeSequence", "numericSeparatorInEscapeSequence", "unexpectedNumericSeparator", "invalidDigit", "invalidCodePoint", "tail", "quasis", "delegate", "assertOptionalChainStart", "readonly", "variance"], "sources": ["../../src/definitions/core.ts"], "sourcesContent": ["import is from \"../validators/is.ts\";\nimport isValidIdentifier from \"../validators/isValidIdentifier.ts\";\nimport { isKeyword, isReservedWord } from \"@babel/helper-validator-identifier\";\nimport type * as t from \"../index.ts\";\nimport { readStringContents } from \"@babel/helper-string-parser\";\n\nimport {\n  BINARY_OPERATORS,\n  LOGICAL_OPERATORS,\n  ASSIGNMENT_OPERATORS,\n  UNARY_OPERATORS,\n  UPDATE_OPERATORS,\n} from \"../constants/index.ts\";\n\nimport {\n  defineAliasedType,\n  assertShape,\n  assertOptionalChainStart,\n  assertValueType,\n  assertNodeType,\n  assertNodeOrValueType,\n  assertEach,\n  chain,\n  assertOneOf,\n  validateOptional,\n  type Validator,\n  arrayOf,\n  arrayOfType,\n  validateArrayOfType,\n  validateType,\n} from \"./utils.ts\";\n\nconst defineType = defineAliasedType(\"Standardized\");\n\ndefineType(\"ArrayExpression\", {\n  fields: {\n    elements: {\n      validate: arrayOf(\n        assertNodeOrValueType(\"null\", \"Expression\", \"SpreadElement\"),\n      ),\n      default:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? []\n          : undefined,\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"AssignmentExpression\", {\n  fields: {\n    operator: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertValueType(\"string\")\n          : Object.assign(\n              (function () {\n                const identifier = assertOneOf(...ASSIGNMENT_OPERATORS);\n                const pattern = assertOneOf(\"=\");\n\n                return function (node: t.AssignmentExpression, key, val) {\n                  const validator = is(\"Pattern\", node.left)\n                    ? pattern\n                    : identifier;\n                  validator(node, key, val);\n                } as Validator;\n              })(),\n              { oneOf: ASSIGNMENT_OPERATORS },\n            ),\n    },\n    left: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertNodeType(\"LVal\")\n          : assertNodeType(\n              \"Identifier\",\n              \"MemberExpression\",\n              \"OptionalMemberExpression\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"TSAsExpression\",\n              \"TSSatisfiesExpression\",\n              \"TSTypeAssertion\",\n              \"TSNonNullExpression\",\n            ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"BinaryExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...BINARY_OPERATORS),\n    },\n    left: {\n      validate: (function () {\n        const expression = assertNodeType(\"Expression\");\n        const inOp = assertNodeType(\"Expression\", \"PrivateName\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.BinaryExpression, key, val) {\n            const validator = node.operator === \"in\" ? inOp : expression;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `operator` property\n          { oneOfNodeTypes: [\"Expression\", \"PrivateName\"] },\n        );\n        return validator;\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n});\n\ndefineType(\"InterpreterDirective\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"Directive\", {\n  visitor: [\"value\"],\n  fields: {\n    value: {\n      validate: assertNodeType(\"DirectiveLiteral\"),\n    },\n  },\n});\n\ndefineType(\"DirectiveLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"BlockStatement\", {\n  builder: [\"body\", \"directives\"],\n  visitor: [\"directives\", \"body\"],\n  fields: {\n    directives: {\n      validate: arrayOfType(\"Directive\"),\n      default: [],\n    },\n    body: validateArrayOfType(\"Statement\"),\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\", \"Statement\"],\n});\n\ndefineType(\"BreakStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"CallExpression\", {\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"callee\", \"typeArguments\", \"arguments\"]\n    : [\"callee\", \"typeParameters\", \"typeArguments\", \"arguments\"],\n  builder: [\"callee\", \"arguments\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\", \"Super\", \"V8IntrinsicIdentifier\"),\n    },\n    arguments: validateArrayOfType(\n      \"Expression\",\n      \"SpreadElement\",\n      \"ArgumentPlaceholder\",\n    ),\n    typeArguments: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterInstantiation\",\n            \"TSTypeParameterInstantiation\",\n          )\n        : assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    ...(process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n      ? {}\n      : {\n          optional: {\n            validate: assertValueType(\"boolean\"),\n            optional: true,\n          },\n          typeParameters: {\n            validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\ndefineType(\"CatchClause\", {\n  visitor: [\"param\", \"body\"],\n  fields: {\n    param: {\n      validate: assertNodeType(\"Identifier\", \"ArrayPattern\", \"ObjectPattern\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\"],\n});\n\ndefineType(\"ConditionalExpression\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    alternate: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\", \"Conditional\"],\n});\n\ndefineType(\"ContinueStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"DebuggerStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"DoWhileStatement\", {\n  builder: [\"test\", \"body\"],\n  visitor: [\"body\", \"test\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n});\n\ndefineType(\"EmptyStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"ExpressionStatement\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Statement\", \"ExpressionWrapper\"],\n});\n\ndefineType(\"File\", {\n  builder: [\"program\", \"comments\", \"tokens\"],\n  visitor: [\"program\"],\n  fields: {\n    program: {\n      validate: assertNodeType(\"Program\"),\n    },\n    comments: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? Object.assign(() => {}, {\n              each: { oneOfNodeTypes: [\"CommentBlock\", \"CommentLine\"] },\n            })\n          : assertEach(assertNodeType(\"CommentBlock\", \"CommentLine\")),\n      optional: true,\n    },\n    tokens: {\n      // todo(ts): add Token type\n      validate: assertEach(Object.assign(() => {}, { type: \"any\" })),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForInStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertNodeType(\"VariableDeclaration\", \"LVal\")\n          : assertNodeType(\n              \"VariableDeclaration\",\n              \"Identifier\",\n              \"MemberExpression\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"TSAsExpression\",\n              \"TSSatisfiesExpression\",\n              \"TSTypeAssertion\",\n              \"TSNonNullExpression\",\n            ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"ForStatement\", {\n  visitor: [\"init\", \"test\", \"update\", \"body\"],\n  aliases: [\"Scopable\", \"Statement\", \"For\", \"BlockParent\", \"Loop\"],\n  fields: {\n    init: {\n      validate: assertNodeType(\"VariableDeclaration\", \"Expression\"),\n      optional: true,\n    },\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    update: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\nexport const functionCommon = () => ({\n  params: validateArrayOfType(\"FunctionParameter\"),\n  generator: {\n    default: false,\n  },\n  async: {\n    default: false,\n  },\n});\n\nexport const functionTypeAnnotationCommon = () => ({\n  returnType: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  typeParameters: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeParameterDeclaration\", \"TSTypeParameterDeclaration\")\n      : assertNodeType(\n          \"TypeParameterDeclaration\",\n          \"TSTypeParameterDeclaration\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n});\n\nexport const functionDeclarationCommon = () => ({\n  ...functionCommon(),\n  declare: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  id: {\n    validate: assertNodeType(\"Identifier\"),\n    optional: true, // May be null for `export default function`\n  },\n});\n\ndefineType(\"FunctionDeclaration\", {\n  builder: [\"id\", \"params\", \"body\", \"generator\", \"async\"],\n  visitor: [\n    \"id\",\n    \"typeParameters\",\n    \"params\",\n    \"predicate\",\n    \"returnType\",\n    \"body\",\n  ],\n  fields: {\n    ...functionDeclarationCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Statement\",\n    \"Pureish\",\n    \"Declaration\",\n  ],\n  validate:\n    !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? undefined\n      : (function () {\n          const identifier = assertNodeType(\"Identifier\");\n\n          return function (parent, key, node) {\n            if (!is(\"ExportDefaultDeclaration\", parent)) {\n              identifier(node, \"id\", node.id);\n            }\n          };\n        })(),\n});\n\ndefineType(\"FunctionExpression\", {\n  inherits: \"FunctionDeclaration\",\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\nexport const patternLikeCommon = () => ({\n  typeAnnotation: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  optional: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  decorators: {\n    validate: arrayOfType(\"Decorator\"),\n    optional: true,\n  },\n});\n\ndefineType(\"Identifier\", {\n  builder: [\"name\"],\n  visitor: [\"typeAnnotation\", \"decorators\" /* for legacy param decorators */],\n  aliases: [\n    \"Expression\",\n    \"FunctionParameter\",\n    \"PatternLike\",\n    \"LVal\",\n    \"TSEntityName\",\n  ],\n  fields: {\n    ...patternLikeCommon(),\n    name: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertValueType(\"string\"),\n              Object.assign(\n                function (node, key, val) {\n                  if (!isValidIdentifier(val, false)) {\n                    throw new TypeError(\n                      `\"${val}\" is not a valid identifier name`,\n                    );\n                  }\n                } as Validator,\n                { type: \"string\" },\n              ),\n            )\n          : assertValueType(\"string\"),\n    },\n  },\n  validate:\n    process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n      ? function (parent, key, node) {\n          const match = /\\.(\\w+)$/.exec(key.toString());\n          if (!match) return;\n\n          const [, parentKey] = match;\n          const nonComp = { computed: false };\n\n          // We can't check if `parent.property === node`, because nodes are validated\n          // before replacing them in the AST.\n          if (parentKey === \"property\") {\n            if (is(\"MemberExpression\", parent, nonComp)) return;\n            if (is(\"OptionalMemberExpression\", parent, nonComp)) return;\n          } else if (parentKey === \"key\") {\n            if (is(\"Property\", parent, nonComp)) return;\n            if (is(\"Method\", parent, nonComp)) return;\n          } else if (parentKey === \"exported\") {\n            if (is(\"ExportSpecifier\", parent)) return;\n          } else if (parentKey === \"imported\") {\n            if (is(\"ImportSpecifier\", parent, { imported: node })) return;\n          } else if (parentKey === \"meta\") {\n            if (is(\"MetaProperty\", parent, { meta: node })) return;\n          }\n\n          if (\n            // Ideally we should call isStrictReservedWord if this node is a descendant\n            // of a block in strict mode. Also, we should pass the inModule option so\n            // we can disable \"await\" in module.\n            (isKeyword(node.name) || isReservedWord(node.name, false)) &&\n            // Even if \"this\" is a keyword, we are using the Identifier\n            // node to represent it.\n            node.name !== \"this\"\n          ) {\n            throw new TypeError(`\"${node.name}\" is not a valid identifier`);\n          }\n        }\n      : undefined,\n});\n\ndefineType(\"IfStatement\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  aliases: [\"Statement\", \"Conditional\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    alternate: {\n      optional: true,\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"LabeledStatement\", {\n  visitor: [\"label\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"StringLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NumericLiteral\", {\n  builder: [\"value\"],\n  deprecatedAlias: \"NumberLiteral\",\n  fields: {\n    value: {\n      validate: chain(\n        assertValueType(\"number\"),\n        Object.assign(\n          function (node, key, val) {\n            if (1 / val < 0 || !Number.isFinite(val)) {\n              const error = new Error(\n                \"NumericLiterals must be non-negative finite numbers. \" +\n                  `You can use t.valueToNode(${val}) instead.`,\n              );\n              if (process.env.BABEL_8_BREAKING) {\n                // TODO(@nicolo-ribaudo) Fix regenerator to not pass negative\n                // numbers here.\n                if (!IS_STANDALONE) {\n                  if (!new Error().stack.includes(\"regenerator\")) {\n                    throw error;\n                  }\n                }\n              } else {\n                // TODO: Enable this warning once regenerator is fixed.\n                // https://github.com/facebook/regenerator/pull/680\n                // console.warn(error);\n              }\n            }\n          } satisfies Validator,\n          { type: \"number\" },\n        ),\n      ),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NullLiteral\", {\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"BooleanLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"boolean\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"RegExpLiteral\", {\n  builder: [\"pattern\", \"flags\"],\n  deprecatedAlias: \"RegexLiteral\",\n  aliases: [\"Expression\", \"Pureish\", \"Literal\"],\n  fields: {\n    pattern: {\n      validate: assertValueType(\"string\"),\n    },\n    flags: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertValueType(\"string\"),\n              Object.assign(\n                function (node, key, val) {\n                  const invalid = /[^gimsuy]/.exec(val);\n                  if (invalid) {\n                    throw new TypeError(\n                      `\"${invalid[0]}\" is not a valid RegExp flag`,\n                    );\n                  }\n                } as Validator,\n                { type: \"string\" },\n              ),\n            )\n          : assertValueType(\"string\"),\n      default: \"\",\n    },\n  },\n});\n\ndefineType(\"LogicalExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...LOGICAL_OPERATORS),\n    },\n    left: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"MemberExpression\", {\n  builder: [\n    \"object\",\n    \"property\",\n    \"computed\",\n    ...(!process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? [\"optional\"]\n      : []),\n  ],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\", \"Super\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\", \"PrivateName\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (\n          node: t.MemberExpression,\n          key,\n          val,\n        ) {\n          const validator: Validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\"Expression\", \"Identifier\", \"PrivateName\"];\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    ...(!process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          optional: {\n            validate: assertValueType(\"boolean\"),\n            optional: true,\n          },\n        }\n      : {}),\n  },\n});\n\ndefineType(\"NewExpression\", { inherits: \"CallExpression\" });\n\ndefineType(\"Program\", {\n  // Note: We explicitly leave 'interpreter' out here because it is\n  // conceptually comment-like, and Babel does not traverse comments either.\n  visitor: [\"directives\", \"body\"],\n  builder: [\"body\", \"directives\", \"sourceType\", \"interpreter\"],\n  fields: {\n    sourceType: {\n      validate: assertOneOf(\"script\", \"module\"),\n      default: \"script\",\n    },\n    interpreter: {\n      validate: assertNodeType(\"InterpreterDirective\"),\n      default: null,\n      optional: true,\n    },\n    directives: {\n      validate: arrayOfType(\"Directive\"),\n      default: [],\n    },\n    body: validateArrayOfType(\"Statement\"),\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\"],\n});\n\ndefineType(\"ObjectExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: validateArrayOfType(\n      \"ObjectMethod\",\n      \"ObjectProperty\",\n      \"SpreadElement\",\n    ),\n  },\n});\n\ndefineType(\"ObjectMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"computed\", \"generator\", \"async\"],\n  visitor: [\n    \"decorators\",\n    \"key\",\n    \"typeParameters\",\n    \"params\",\n    \"returnType\",\n    \"body\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"method\", \"get\", \"set\"),\n      ...(!process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n        ? { default: \"method\" }\n        : {}),\n    },\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (node: t.ObjectMethod, key, val) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\n          \"Expression\",\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        ];\n        return validator;\n      })(),\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  aliases: [\n    \"UserWhitespacable\",\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"ObjectMember\",\n  ],\n});\n\ndefineType(\"ObjectProperty\", {\n  builder: [\n    \"key\",\n    \"value\",\n    \"computed\",\n    \"shorthand\",\n    ...(!process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? [\"decorators\"]\n      : []),\n  ],\n  fields: {\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = process.env.BABEL_8_BREAKING\n          ? assertNodeType(\n              \"Identifier\",\n              \"StringLiteral\",\n              \"NumericLiteral\",\n              \"BigIntLiteral\",\n              \"PrivateName\",\n            )\n          : assertNodeType(\n              \"Identifier\",\n              \"StringLiteral\",\n              \"NumericLiteral\",\n              \"BigIntLiteral\",\n              // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n              \"DecimalLiteral\",\n              \"PrivateName\",\n            );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.ObjectProperty, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          {\n            // todo(ts): can be discriminated union by `computed` property\n            oneOfNodeTypes: process.env.BABEL_8_BREAKING\n              ? [\n                  \"Expression\",\n                  \"Identifier\",\n                  \"StringLiteral\",\n                  \"NumericLiteral\",\n                  \"BigIntLiteral\",\n                  \"PrivateName\",\n                ]\n              : [\n                  \"Expression\",\n                  \"Identifier\",\n                  \"StringLiteral\",\n                  \"NumericLiteral\",\n                  \"BigIntLiteral\",\n                  \"DecimalLiteral\",\n                  \"PrivateName\",\n                ],\n          },\n        );\n        return validator;\n      })(),\n    },\n    value: {\n      // Value may be PatternLike if this is an AssignmentProperty\n      // https://github.com/babel/babylon/issues/434\n      validate: assertNodeType(\"Expression\", \"PatternLike\"),\n    },\n    shorthand: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertValueType(\"boolean\"),\n              Object.assign(\n                function (node: t.ObjectProperty, key, shorthand) {\n                  if (!shorthand) return;\n\n                  if (node.computed) {\n                    throw new TypeError(\n                      \"Property shorthand of ObjectProperty cannot be true if computed is true\",\n                    );\n                  }\n\n                  if (!is(\"Identifier\", node.key)) {\n                    throw new TypeError(\n                      \"Property shorthand of ObjectProperty cannot be true if key is not an Identifier\",\n                    );\n                  }\n                } as Validator,\n                { type: \"boolean\" },\n              ),\n            )\n          : assertValueType(\"boolean\"),\n      default: false,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n  },\n  visitor: [\"decorators\", \"key\", \"value\"],\n  aliases: [\"UserWhitespacable\", \"Property\", \"ObjectMember\"],\n  validate:\n    !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? undefined\n      : (function () {\n          const pattern = assertNodeType(\n            \"Identifier\",\n            \"Pattern\",\n            \"TSAsExpression\",\n            \"TSSatisfiesExpression\",\n            \"TSNonNullExpression\",\n            \"TSTypeAssertion\",\n          );\n          const expression = assertNodeType(\"Expression\");\n\n          return function (parent, key, node) {\n            const validator = is(\"ObjectPattern\", parent)\n              ? pattern\n              : expression;\n            validator(node, \"value\", node.value);\n          };\n        })(),\n});\n\ndefineType(\"RestElement\", {\n  visitor: [\"argument\", \"typeAnnotation\"],\n  builder: [\"argument\"],\n  aliases: process.env.BABEL_8_BREAKING\n    ? [\"FunctionParameter\", \"PatternLike\"]\n    : [\"FunctionParameter\", \"PatternLike\", \"LVal\"],\n  deprecatedAlias: \"RestProperty\",\n  fields: {\n    ...patternLikeCommon(),\n    argument: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertNodeType(\n              \"Identifier\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"MemberExpression\",\n              \"TSAsExpression\",\n              \"TSSatisfiesExpression\",\n              \"TSTypeAssertion\",\n              \"TSNonNullExpression\",\n              // These are not valid in RestElement, but we allow them for backwards compatibility.\n              \"RestElement\",\n              \"AssignmentPattern\",\n            )\n          : assertNodeType(\n              \"Identifier\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"MemberExpression\",\n              \"TSAsExpression\",\n              \"TSSatisfiesExpression\",\n              \"TSTypeAssertion\",\n              \"TSNonNullExpression\",\n            ),\n    },\n  },\n  validate:\n    process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n      ? function (parent: t.ArrayPattern | t.ObjectPattern, key) {\n          const match = /(\\w+)\\[(\\d+)\\]/.exec(key.toString());\n          if (!match) throw new Error(\"Internal Babel error: malformed key.\");\n\n          const [, listKey, index] = match as unknown as [\n            string,\n            keyof typeof parent,\n            string,\n          ];\n          if ((parent[listKey] as t.Node[]).length > +index + 1) {\n            throw new TypeError(\n              `RestElement must be last element of ${listKey}`,\n            );\n          }\n        }\n      : undefined,\n});\n\ndefineType(\"ReturnStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"SequenceExpression\", {\n  visitor: [\"expressions\"],\n  fields: {\n    expressions: validateArrayOfType(\"Expression\"),\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"ParenthesizedExpression\", {\n  visitor: [\"expression\"],\n  aliases: [\"Expression\", \"ExpressionWrapper\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"SwitchCase\", {\n  visitor: [\"test\", \"consequent\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    consequent: validateArrayOfType(\"Statement\"),\n  },\n});\n\ndefineType(\"SwitchStatement\", {\n  visitor: [\"discriminant\", \"cases\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Scopable\"],\n  fields: {\n    discriminant: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    cases: validateArrayOfType(\"SwitchCase\"),\n  },\n});\n\ndefineType(\"ThisExpression\", {\n  aliases: process.env.BABEL_8_BREAKING\n    ? [\"Expression\", \"TSEntityName\"]\n    : [\"Expression\"],\n});\n\ndefineType(\"ThrowStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"TryStatement\", {\n  visitor: [\"block\", \"handler\", \"finalizer\"],\n  aliases: [\"Statement\"],\n  fields: {\n    block: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertNodeType(\"BlockStatement\"),\n              Object.assign(\n                function (node: t.TryStatement) {\n                  // This validator isn't put at the top level because we can run it\n                  // even if this node doesn't have a parent.\n\n                  if (!node.handler && !node.finalizer) {\n                    throw new TypeError(\n                      \"TryStatement expects either a handler or finalizer, or both\",\n                    );\n                  }\n                } as Validator,\n                { oneOfNodeTypes: [\"BlockStatement\"] },\n              ),\n            )\n          : assertNodeType(\"BlockStatement\"),\n    },\n    handler: {\n      optional: true,\n      validate: assertNodeType(\"CatchClause\"),\n    },\n    finalizer: {\n      optional: true,\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"UnaryExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: true,\n    },\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UNARY_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\", \"Expression\"],\n});\n\ndefineType(\"UpdateExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: false,\n    },\n    argument: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertNodeType(\"Expression\")\n          : assertNodeType(\"Identifier\", \"MemberExpression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UPDATE_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"VariableDeclaration\", {\n  builder: [\"kind\", \"declarations\"],\n  visitor: [\"declarations\"],\n  aliases: [\"Statement\", \"Declaration\"],\n  fields: {\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    kind: {\n      validate: assertOneOf(\n        \"var\",\n        \"let\",\n        \"const\",\n        // https://github.com/tc39/proposal-explicit-resource-management\n        \"using\",\n        // https://github.com/tc39/proposal-async-explicit-resource-management\n        \"await using\",\n      ),\n    },\n    declarations: validateArrayOfType(\"VariableDeclarator\"),\n  },\n  validate:\n    process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n      ? (() => {\n          const withoutInit = assertNodeType(\"Identifier\", \"Placeholder\");\n          const constOrLetOrVar = assertNodeType(\n            \"Identifier\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"Placeholder\",\n          );\n          const usingOrAwaitUsing = assertNodeType(\n            \"Identifier\",\n            \"VoidPattern\",\n            \"Placeholder\",\n          );\n\n          return function (parent, key, node: t.VariableDeclaration) {\n            const { kind, declarations } = node;\n            const parentIsForX = is(\"ForXStatement\", parent, { left: node });\n            if (parentIsForX) {\n              if (declarations.length !== 1) {\n                throw new TypeError(\n                  `Exactly one VariableDeclarator is required in the VariableDeclaration of a ${parent.type}`,\n                );\n              }\n            }\n            for (const decl of declarations) {\n              if (kind === \"const\" || kind === \"let\" || kind === \"var\") {\n                if (!parentIsForX && !decl.init) {\n                  withoutInit(decl, \"id\", decl.id);\n                } else {\n                  constOrLetOrVar(decl, \"id\", decl.id);\n                }\n              } else {\n                usingOrAwaitUsing(decl, \"id\", decl.id);\n              }\n            }\n          };\n        })()\n      : undefined,\n});\n\ndefineType(\"VariableDeclarator\", {\n  visitor: [\"id\", \"init\"],\n  fields: {\n    id: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertNodeType(\"LVal\", \"VoidPattern\")\n          : assertNodeType(\n              \"Identifier\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"VoidPattern\",\n            ),\n    },\n    definite: {\n      optional: true,\n      validate: assertValueType(\"boolean\"),\n    },\n    init: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"WhileStatement\", {\n  visitor: [\"test\", \"body\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"WithStatement\", {\n  visitor: [\"object\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\n// --- ES2015 ---\ndefineType(\"AssignmentPattern\", {\n  visitor: [\"left\", \"right\", \"decorators\" /* for legacy param decorators */],\n  builder: [\"left\", \"right\"],\n  aliases: process.env.BABEL_8_BREAKING\n    ? [\"FunctionParameter\", \"Pattern\", \"PatternLike\"]\n    : [\"FunctionParameter\", \"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    left: {\n      validate: assertNodeType(\n        \"Identifier\",\n        \"ObjectPattern\",\n        \"ArrayPattern\",\n        \"MemberExpression\",\n        \"TSAsExpression\",\n        \"TSSatisfiesExpression\",\n        \"TSTypeAssertion\",\n        \"TSNonNullExpression\",\n      ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    // For TypeScript\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ArrayPattern\", {\n  visitor: [\"elements\", \"typeAnnotation\"],\n  builder: [\"elements\"],\n  aliases: [\"FunctionParameter\", \"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeOrValueType(\"null\", \"PatternLike\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ArrowFunctionExpression\", {\n  builder: [\"params\", \"body\", \"async\"],\n  visitor: [\"typeParameters\", \"params\", \"predicate\", \"returnType\", \"body\"],\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    expression: {\n      // https://github.com/babel/babylon/issues/505\n      validate: assertValueType(\"boolean\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\", \"Expression\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassBody\", {\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\n      \"ClassMethod\",\n      \"ClassPrivateMethod\",\n      \"ClassProperty\",\n      \"ClassPrivateProperty\",\n      \"ClassAccessorProperty\",\n      \"TSDeclareMethod\",\n      \"TSIndexSignature\",\n      \"StaticBlock\",\n    ),\n  },\n});\n\ndefineType(\"ClassExpression\", {\n  builder: [\"id\", \"superClass\", \"body\", \"decorators\"],\n  visitor: [\n    \"decorators\",\n    \"id\",\n    \"typeParameters\",\n    \"superClass\",\n    process.env.BABEL_8_BREAKING ? \"superTypeArguments\" : \"superTypeParameters\",\n    \"mixins\",\n    \"implements\",\n    \"body\",\n  ],\n  aliases: [\"Scopable\", \"Class\", \"Expression\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    [process.env.BABEL_8_BREAKING\n      ? \"superTypeArguments\"\n      : \"superTypeParameters\"]: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: arrayOfType(\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        process.env.BABEL_8_BREAKING\n          ? \"TSClassImplements\"\n          : \"TSExpressionWithTypeArguments\",\n        \"ClassImplements\",\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassDeclaration\", {\n  inherits: \"ClassExpression\",\n  aliases: [\"Scopable\", \"Class\", \"Statement\", \"Declaration\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      // The id may be omitted if this is the child of an\n      // ExportDefaultDeclaration.\n      optional: true,\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    [process.env.BABEL_8_BREAKING\n      ? \"superTypeArguments\"\n      : \"superTypeParameters\"]: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: arrayOfType(\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        process.env.BABEL_8_BREAKING\n          ? \"TSClassImplements\"\n          : \"TSExpressionWithTypeArguments\",\n        \"ClassImplements\",\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    abstract: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n  validate:\n    !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? undefined\n      : (function () {\n          const identifier = assertNodeType(\"Identifier\");\n          return function (parent, key, node) {\n            if (!is(\"ExportDefaultDeclaration\", parent)) {\n              identifier(node, \"id\", node.id);\n            }\n          };\n        })(),\n});\n\nexport const importAttributes = {\n  attributes: {\n    optional: true,\n    validate: arrayOfType(\"ImportAttribute\"),\n  },\n  assertions: {\n    deprecated: true,\n    optional: true,\n    validate: arrayOfType(\"ImportAttribute\"),\n  },\n};\n\ndefineType(\"ExportAllDeclaration\", {\n  builder: [\"source\"],\n  visitor: [\"source\", \"attributes\", \"assertions\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n    ...importAttributes,\n  },\n});\n\ndefineType(\"ExportDefaultDeclaration\", {\n  visitor: [\"declaration\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: validateType(\n      \"TSDeclareFunction\",\n      \"FunctionDeclaration\",\n      \"ClassDeclaration\",\n      \"Expression\",\n    ),\n    exportKind: validateOptional(assertOneOf(\"value\")),\n  },\n});\n\ndefineType(\"ExportNamedDeclaration\", {\n  builder: [\"declaration\", \"specifiers\", \"source\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"declaration\", \"specifiers\", \"source\", \"attributes\"]\n    : [\"declaration\", \"specifiers\", \"source\", \"attributes\", \"assertions\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: {\n      optional: true,\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertNodeType(\"Declaration\"),\n              Object.assign(\n                function (node: t.ExportNamedDeclaration, key, val) {\n                  // This validator isn't put at the top level because we can run it\n                  // even if this node doesn't have a parent.\n\n                  if (val && node.specifiers.length) {\n                    throw new TypeError(\n                      \"Only declaration or specifiers is allowed on ExportNamedDeclaration\",\n                    );\n                  }\n\n                  // This validator isn't put at the top level because we can run it\n                  // even if this node doesn't have a parent.\n\n                  if (val && node.source) {\n                    throw new TypeError(\n                      \"Cannot export a declaration from a source\",\n                    );\n                  }\n                } as Validator,\n                { oneOfNodeTypes: [\"Declaration\"] },\n              ),\n            )\n          : assertNodeType(\"Declaration\"),\n    },\n    ...importAttributes,\n    specifiers: {\n      default: [],\n      validate: arrayOf(\n        (function () {\n          const sourced = assertNodeType(\n            \"ExportSpecifier\",\n            \"ExportDefaultSpecifier\",\n            \"ExportNamespaceSpecifier\",\n          );\n          const sourceless = assertNodeType(\"ExportSpecifier\");\n\n          if (\n            !process.env.BABEL_8_BREAKING &&\n            !process.env.BABEL_TYPES_8_BREAKING\n          )\n            return sourced;\n\n          return Object.assign(\n            function (node: t.ExportNamedDeclaration, key, val) {\n              const validator = node.source ? sourced : sourceless;\n              validator(node, key, val);\n            } as Validator,\n            {\n              oneOfNodeTypes: [\n                \"ExportSpecifier\",\n                \"ExportDefaultSpecifier\",\n                \"ExportNamespaceSpecifier\",\n              ],\n            },\n          );\n        })(),\n      ),\n    },\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n      optional: true,\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n  },\n});\n\ndefineType(\"ExportSpecifier\", {\n  visitor: [\"local\", \"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    exported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    exportKind: {\n      // And TypeScript's \"export { type foo } from\"\n      validate: assertOneOf(\"type\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForOfStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  builder: [\"left\", \"right\", \"body\", \"await\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate: (function () {\n        if (\n          !process.env.BABEL_8_BREAKING &&\n          !process.env.BABEL_TYPES_8_BREAKING\n        ) {\n          return assertNodeType(\"VariableDeclaration\", \"LVal\");\n        }\n\n        const declaration = assertNodeType(\"VariableDeclaration\");\n        const lval = assertNodeType(\n          \"Identifier\",\n          \"MemberExpression\",\n          \"ArrayPattern\",\n          \"ObjectPattern\",\n          \"TSAsExpression\",\n          \"TSSatisfiesExpression\",\n          \"TSTypeAssertion\",\n          \"TSNonNullExpression\",\n        );\n\n        return Object.assign(\n          function (node, key, val) {\n            if (is(\"VariableDeclaration\", val)) {\n              declaration(node, key, val);\n            } else {\n              lval(node, key, val);\n            }\n          } as Validator,\n          {\n            oneOfNodeTypes: [\n              \"VariableDeclaration\",\n              \"Identifier\",\n              \"MemberExpression\",\n              \"ArrayPattern\",\n              \"ObjectPattern\",\n              \"TSAsExpression\",\n              \"TSSatisfiesExpression\",\n              \"TSTypeAssertion\",\n              \"TSNonNullExpression\",\n            ],\n          },\n        );\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    await: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ImportDeclaration\", {\n  builder: [\"specifiers\", \"source\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"specifiers\", \"source\", \"attributes\"]\n    : [\"specifiers\", \"source\", \"attributes\", \"assertions\"],\n  aliases: [\"Statement\", \"Declaration\", \"ImportOrExportDeclaration\"],\n  fields: {\n    ...importAttributes,\n    module: {\n      optional: true,\n      validate: assertValueType(\"boolean\"),\n    },\n    phase: {\n      default: null,\n      validate: assertOneOf(\"source\", \"defer\"),\n    },\n    specifiers: validateArrayOfType(\n      \"ImportSpecifier\",\n      \"ImportDefaultSpecifier\",\n      \"ImportNamespaceSpecifier\",\n    ),\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    importKind: {\n      // Handle TypeScript/Flowtype's extension \"import type foo from\"\n      // TypeScript doesn't support typeof\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ImportDefaultSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportNamespaceSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportSpecifier\", {\n  visitor: [\"imported\", \"local\"],\n  builder: [\"local\", \"imported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    imported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    importKind: {\n      // Handle Flowtype's extension \"import {typeof foo} from\"\n      // And TypeScript's \"import { type foo } from\"\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ImportExpression\", {\n  visitor: [\"source\", \"options\"],\n  aliases: [\"Expression\"],\n  fields: {\n    phase: {\n      default: null,\n      validate: assertOneOf(\"source\", \"defer\"),\n    },\n    source: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    options: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"MetaProperty\", {\n  visitor: [\"meta\", \"property\"],\n  aliases: [\"Expression\"],\n  fields: {\n    meta: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertNodeType(\"Identifier\"),\n              Object.assign(\n                function (node: t.MetaProperty, key, val) {\n                  let property;\n                  switch (val.name) {\n                    case \"function\":\n                      property = \"sent\";\n                      break;\n                    case \"new\":\n                      property = \"target\";\n                      break;\n                    case \"import\":\n                      property = \"meta\";\n                      break;\n                  }\n                  if (!is(\"Identifier\", node.property, { name: property })) {\n                    throw new TypeError(\"Unrecognised MetaProperty\");\n                  }\n                } as Validator,\n                { oneOfNodeTypes: [\"Identifier\"] },\n              ),\n            )\n          : assertNodeType(\"Identifier\"),\n    },\n    property: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\nexport const classMethodOrPropertyCommon = () => ({\n  abstract: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  accessibility: {\n    validate: assertOneOf(\"public\", \"private\", \"protected\"),\n    optional: true,\n  },\n  static: {\n    default: false,\n  },\n  override: {\n    default: false,\n  },\n  computed: {\n    default: false,\n  },\n  optional: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  key: {\n    validate: chain(\n      (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        return function (node: any, key: string, val: any) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n      })(),\n      assertNodeType(\n        \"Identifier\",\n        \"StringLiteral\",\n        \"NumericLiteral\",\n        \"BigIntLiteral\",\n        \"Expression\",\n      ),\n    ),\n  },\n});\n\nexport const classMethodOrDeclareMethodCommon = () => ({\n  ...functionCommon(),\n  ...classMethodOrPropertyCommon(),\n  params: validateArrayOfType(\"FunctionParameter\", \"TSParameterProperty\"),\n  kind: {\n    validate: assertOneOf(\"get\", \"set\", \"method\", \"constructor\"),\n    default: \"method\",\n  },\n  access: {\n    validate: chain(\n      assertValueType(\"string\"),\n      assertOneOf(\"public\", \"private\", \"protected\"),\n    ),\n    optional: true,\n  },\n  decorators: {\n    validate: arrayOfType(\"Decorator\"),\n    optional: true,\n  },\n});\n\ndefineType(\"ClassMethod\", {\n  aliases: [\"Function\", \"Scopable\", \"BlockParent\", \"FunctionParent\", \"Method\"],\n  builder: [\n    \"kind\",\n    \"key\",\n    \"params\",\n    \"body\",\n    \"computed\",\n    \"static\",\n    \"generator\",\n    \"async\",\n  ],\n  visitor: [\n    \"decorators\",\n    \"key\",\n    \"typeParameters\",\n    \"params\",\n    \"returnType\",\n    \"body\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"ObjectPattern\", {\n  visitor: [\n    \"decorators\" /* for legacy param decorators */,\n    \"properties\",\n    \"typeAnnotation\",\n  ],\n  builder: [\"properties\"],\n  aliases: [\"FunctionParameter\", \"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    properties: validateArrayOfType(\"RestElement\", \"ObjectProperty\"),\n  },\n});\n\ndefineType(\"SpreadElement\", {\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\"],\n  deprecatedAlias: \"SpreadProperty\",\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\n  \"Super\",\n  process.env.BABEL_8_BREAKING\n    ? undefined\n    : {\n        aliases: [\"Expression\"],\n      },\n);\n\ndefineType(\"TaggedTemplateExpression\", {\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"tag\", \"typeArguments\", \"quasi\"]\n    : [\"tag\", \"typeParameters\", \"quasi\"],\n  builder: [\"tag\", \"quasi\"],\n  aliases: [\"Expression\"],\n  fields: {\n    tag: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    quasi: {\n      validate: assertNodeType(\"TemplateLiteral\"),\n    },\n    [process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\"]: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TemplateElement\", {\n  builder: [\"value\", \"tail\"],\n  fields: {\n    value: {\n      validate: chain(\n        assertShape({\n          raw: {\n            validate: assertValueType(\"string\"),\n          },\n          cooked: {\n            validate: assertValueType(\"string\"),\n            optional: true,\n          },\n        }),\n        function templateElementCookedValidator(node: t.TemplateElement) {\n          const raw = node.value.raw;\n\n          let unterminatedCalled = false;\n\n          const error = () => {\n            // unreachable\n            throw new Error(\"Internal @babel/types error.\");\n          };\n          const { str, firstInvalidLoc } = readStringContents(\n            \"template\",\n            raw,\n            0,\n            0,\n            0,\n            {\n              unterminated() {\n                unterminatedCalled = true;\n              },\n              strictNumericEscape: error,\n              invalidEscapeSequence: error,\n              numericSeparatorInEscapeSequence: error,\n              unexpectedNumericSeparator: error,\n              invalidDigit: error,\n              invalidCodePoint: error,\n            },\n          );\n          if (!unterminatedCalled) throw new Error(\"Invalid raw\");\n\n          node.value.cooked = firstInvalidLoc ? null : str;\n        },\n      ),\n    },\n    tail: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"TemplateLiteral\", {\n  visitor: [\"quasis\", \"expressions\"],\n  aliases: [\"Expression\", \"Literal\"],\n  fields: {\n    quasis: validateArrayOfType(\"TemplateElement\"),\n    expressions: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            // For TypeScript template literal types\n            \"TSType\",\n          ),\n        ),\n        function (node: t.TemplateLiteral, key, val) {\n          if (node.quasis.length !== val.length + 1) {\n            throw new TypeError(\n              `Number of ${\n                node.type\n              } quasis should be exactly one more than the number of expressions.\\nExpected ${\n                val.length + 1\n              } quasis but got ${node.quasis.length}`,\n            );\n          }\n        } as Validator,\n      ),\n    },\n  },\n});\n\ndefineType(\"YieldExpression\", {\n  builder: [\"argument\", \"delegate\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    delegate: {\n      validate:\n        process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n          ? chain(\n              assertValueType(\"boolean\"),\n              Object.assign(\n                function (node: t.YieldExpression, key, val) {\n                  if (val && !node.argument) {\n                    throw new TypeError(\n                      \"Property delegate of YieldExpression cannot be true if there is no argument\",\n                    );\n                  }\n                } as Validator,\n                { type: \"boolean\" },\n              ),\n            )\n          : assertValueType(\"boolean\"),\n      default: false,\n    },\n    argument: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2017 ---\ndefineType(\"AwaitExpression\", {\n  builder: [\"argument\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2019 ---\ndefineType(\"Import\", {\n  aliases: [\"Expression\"],\n});\n\n// --- ES2020 ---\ndefineType(\"BigIntLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertValueType(\"bigint\")\n        : assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"ExportNamespaceSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"OptionalMemberExpression\", {\n  builder: [\"object\", \"property\", \"computed\", \"optional\"],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\", \"LVal\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.OptionalMemberExpression, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `computed` property\n          { oneOfNodeTypes: [\"Expression\", \"Identifier\"] },\n        );\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    optional: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertValueType(\"boolean\")\n          : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n  },\n});\n\ndefineType(\"OptionalCallExpression\", {\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"callee\", \"typeArguments\", \"arguments\"]\n    : [\"callee\", \"typeParameters\", \"typeArguments\", \"arguments\"],\n  builder: [\"callee\", \"arguments\", \"optional\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    arguments: validateArrayOfType(\n      \"Expression\",\n      \"SpreadElement\",\n      \"ArgumentPlaceholder\",\n    ),\n    optional: {\n      validate:\n        !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n          ? assertValueType(\"boolean\")\n          : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n    typeArguments: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterInstantiation\",\n            \"TSTypeParameterInstantiation\",\n          )\n        : assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    ...(process.env.BABEL_8_BREAKING\n      ? {}\n      : {\n          typeParameters: {\n            validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\n// --- ES2022 ---\ndefineType(\"ClassProperty\", {\n  visitor: [\"decorators\", \"variance\", \"key\", \"typeAnnotation\", \"value\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassAccessorProperty\", {\n  visitor: [\"decorators\", \"key\", \"typeAnnotation\", \"value\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\", \"Accessor\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    key: {\n      validate: chain(\n        (function () {\n          const normal = assertNodeType(\n            \"Identifier\",\n            \"StringLiteral\",\n            \"NumericLiteral\",\n            \"BigIntLiteral\",\n            \"PrivateName\",\n          );\n          const computed = assertNodeType(\"Expression\");\n\n          return function (node: any, key: string, val: any) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          };\n        })(),\n        assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n          \"Expression\",\n          \"PrivateName\",\n        ),\n      ),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateProperty\", {\n  visitor: [\"decorators\", \"variance\", \"key\", \"typeAnnotation\", \"value\"],\n  builder: [\"key\", \"value\", \"decorators\", \"static\"],\n  aliases: [\"Property\", \"Private\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n    static: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    optional: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"static\"],\n  visitor: [\n    \"decorators\",\n    \"key\",\n    \"typeParameters\",\n    \"params\",\n    \"returnType\",\n    \"body\",\n  ],\n  aliases: [\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"Private\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"get\", \"set\", \"method\"),\n      default: \"method\",\n    },\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"PrivateName\", {\n  visitor: [\"id\"],\n  aliases: [\"Private\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"StaticBlock\", {\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\"Statement\"),\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"FunctionParent\"],\n});\n\n// --- ES2025 ---\ndefineType(\"ImportAttribute\", {\n  visitor: [\"key\", \"value\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    value: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEA,IAAAG,mBAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAQA,IAAAK,MAAA,GAAAL,OAAA;AAkBA,MAAMM,UAAU,GAAG,IAAAC,wBAAiB,EAAC,cAAc,CAAC;AAEpDD,UAAU,CAAC,iBAAiB,EAAE;EAC5BE,MAAM,EAAE;IACNC,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAC,cAAO,EACf,IAAAC,4BAAqB,EAAC,MAAM,EAAE,YAAY,EAAE,eAAe,CAC7D,CAAC;MACDC,OAAO,EAC4B,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,EAAE,GACFC;IACR;EACF,CAAC;EACDC,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFb,UAAU,CAAC,sBAAsB,EAAE;EACjCE,MAAM,EAAE;IACNY,QAAQ,EAAE;MACRV,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAK,sBAAe,EAAC,QAAQ,CAAC,GACzBC,MAAM,CAACC,MAAM,CACV,YAAY;QACX,MAAMC,UAAU,GAAG,IAAAC,kBAAW,EAAC,GAAGC,2BAAoB,CAAC;QACvD,MAAMC,OAAO,GAAG,IAAAF,kBAAW,EAAC,GAAG,CAAC;QAEhC,OAAO,UAAUG,IAA4B,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACvD,MAAMC,SAAS,GAAG,IAAAC,WAAE,EAAC,SAAS,EAAEJ,IAAI,CAACK,IAAI,CAAC,GACtCN,OAAO,GACPH,UAAU;UACdO,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE,CAAC,EACJ;QAAEI,KAAK,EAAER;MAAqB,CAChC;IACR,CAAC;IACDO,IAAI,EAAE;MACJvB,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAmB,qBAAc,EAAC,MAAM,CAAC,GACtB,IAAAA,qBAAc,EACZ,YAAY,EACZ,kBAAkB,EAClB,0BAA0B,EAC1B,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACR,CAAC;IACDC,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDE,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtCnB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFb,UAAU,CAAC,kBAAkB,EAAE;EAC7B+B,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtC7B,MAAM,EAAE;IACNY,QAAQ,EAAE;MACRV,QAAQ,EAAE,IAAAe,kBAAW,EAAC,GAAGa,uBAAgB;IAC3C,CAAC;IACDL,IAAI,EAAE;MACJvB,QAAQ,EAAG,YAAY;QACrB,MAAM6B,UAAU,GAAG,IAAAJ,qBAAc,EAAC,YAAY,CAAC;QAC/C,MAAMK,IAAI,GAAG,IAAAL,qBAAc,EAAC,YAAY,EAAE,aAAa,CAAC;QAExD,MAAMJ,SAAoB,GAAGT,MAAM,CAACC,MAAM,CACxC,UAAUK,IAAwB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAC5C,MAAMC,SAAS,GAAGH,IAAI,CAACR,QAAQ,KAAK,IAAI,GAAGoB,IAAI,GAAGD,UAAU;UAC5DR,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EAED;UAAEW,cAAc,EAAE,CAAC,YAAY,EAAE,aAAa;QAAE,CAClD,CAAC;QACD,OAAOV,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDK,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDjB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY;AAClC,CAAC,CAAC;AAEFb,UAAU,CAAC,sBAAsB,EAAE;EACjC+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFf,UAAU,CAAC,WAAW,EAAE;EACtBY,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBV,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,kBAAkB;IAC7C;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,kBAAkB,EAAE;EAC7B+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3B+B,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC/BnB,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/BV,MAAM,EAAE;IACNmC,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClC/B,OAAO,EAAE;IACX,CAAC;IACDgC,IAAI,EAAE,IAAAC,0BAAmB,EAAC,WAAW;EACvC,CAAC;EACD3B,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBV,MAAM,EAAE;IACNuC,KAAK,EAAE;MACLrC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF,CAAC;EACD7B,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB;AAChE,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAEH,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,CAAC;EAC9DmB,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;EAChClB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAAc,MAAA,CAAAC,MAAA;IACJ0B,MAAM,EAAE;MACNvC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,OAAO,EAAE,uBAAuB;IACzE,CAAC;IACDe,SAAS,EAAE,IAAAJ,0BAAmB,EAC5B,YAAY,EACZ,eAAe,EACf,qBACF,CAAC;IACDK,aAAa,EAAE;MACbzC,QAAQ,EAKJ,IAAAyB,qBAAc,EAAC,4BAA4B,CAAC;MAChDa,QAAQ,EAAE;IACZ;EAAC,GACmClC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClE,CAAC,CAAC,GACF;IACEgC,QAAQ,EAAE;MACRtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACd1C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,8BAA8B,CAAC;MACxDa,QAAQ,EAAE;IACZ;EACF,CAAC;AAET,CAAC,CAAC;AAEF1C,UAAU,CAAC,aAAa,EAAE;EACxBY,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1BV,MAAM,EAAE;IACN6C,KAAK,EAAE;MACL3C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,cAAc,EAAE,eAAe,CAAC;MACvEa,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C;EACF,CAAC;EACDhB,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa;AACrC,CAAC,CAAC;AAEFb,UAAU,CAAC,uBAAuB,EAAE;EAClCY,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC;EAC5CV,MAAM,EAAE;IACN8C,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDoB,UAAU,EAAE;MACV7C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDqB,SAAS,EAAE;MACT9C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDhB,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa;AACvC,CAAC,CAAC;AAEFb,UAAU,CAAC,mBAAmB,EAAE;EAC9BY,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBV,MAAM,EAAE;IACNuC,KAAK,EAAE;MACLrC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF,CAAC;EACD7B,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB;AAChE,CAAC,CAAC;AAEFb,UAAU,CAAC,mBAAmB,EAAE;EAC9Ba,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFb,UAAU,CAAC,kBAAkB,EAAE;EAC7B+B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACzBnB,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACzBV,MAAM,EAAE;IACN8C,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF,CAAC;EACDhB,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU;AACnE,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3Ba,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFb,UAAU,CAAC,qBAAqB,EAAE;EAChCY,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBV,MAAM,EAAE;IACN+B,UAAU,EAAE;MACV7B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDhB,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB;AAC5C,CAAC,CAAC;AAEFb,UAAU,CAAC,MAAM,EAAE;EACjB+B,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC1CnB,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBV,MAAM,EAAE;IACNiD,OAAO,EAAE;MACP/C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,SAAS;IACpC,CAAC;IACDuB,QAAQ,EAAE;MACRhD,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChEM,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QACtBoC,IAAI,EAAE;UAAElB,cAAc,EAAE,CAAC,cAAc,EAAE,aAAa;QAAE;MAC1D,CAAC,CAAC,GACF,IAAAmB,iBAAU,EAAC,IAAAzB,qBAAc,EAAC,cAAc,EAAE,aAAa,CAAC,CAAC;MAC/Da,QAAQ,EAAE;IACZ,CAAC;IACDa,MAAM,EAAE;MAENnD,QAAQ,EAAE,IAAAkD,iBAAU,EAACtC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAAEuC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;MAC9Dd,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAClCC,OAAO,EAAE,CACP,UAAU,EACV,WAAW,EACX,KAAK,EACL,aAAa,EACb,MAAM,EACN,eAAe,CAChB;EACDX,MAAM,EAAE;IACNyB,IAAI,EAAE;MACJvB,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAmB,qBAAc,EAAC,qBAAqB,EAAE,MAAM,CAAC,GAC7C,IAAAA,qBAAc,EACZ,qBAAqB,EACrB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACR,CAAC;IACDC,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,cAAc,EAAE;EACzBY,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;EAC3CC,OAAO,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC;EAChEX,MAAM,EAAE;IACNuD,IAAI,EAAE;MACJrD,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,qBAAqB,EAAE,YAAY,CAAC;MAC7Da,QAAQ,EAAE;IACZ,CAAC;IACDM,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDgB,MAAM,EAAE;MACNtD,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEK,MAAM8B,cAAc,GAAGA,CAAA,MAAO;EACnCC,MAAM,EAAE,IAAApB,0BAAmB,EAAC,mBAAmB,CAAC;EAChDqB,SAAS,EAAE;IACTtD,OAAO,EAAE;EACX,CAAC;EACDuD,KAAK,EAAE;IACLvD,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAACwD,OAAA,CAAAJ,cAAA,GAAAA,cAAA;AAEI,MAAMK,4BAA4B,GAAGA,CAAA,MAAO;EACjDC,UAAU,EAAE;IACV7D,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ,CAAC;EACDI,cAAc,EAAE;IACd1C,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAACqB,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAEI,MAAME,yBAAyB,GAAGA,CAAA,KAAAlD,MAAA,CAAAC,MAAA,KACpC0C,cAAc,CAAC,CAAC;EACnBQ,OAAO,EAAE;IACP/D,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;IACpC2B,QAAQ,EAAE;EACZ,CAAC;EACD0B,EAAE,EAAE;IACFhE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;IACtCa,QAAQ,EAAE;EACZ;AAAC,EACD;AAACqB,OAAA,CAAAG,yBAAA,GAAAA,yBAAA;AAEHlE,UAAU,CAAC,qBAAqB,EAAE;EAChC+B,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC;EACvDnB,OAAO,EAAE,CACP,IAAI,EACJ,gBAAgB,EAChB,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,MAAM,CACP;EACDV,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDiD,yBAAyB,CAAC,CAAC,EAC3BF,4BAA4B,CAAC,CAAC;IACjCzB,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDwC,SAAS,EAAE;MACTjE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC,EACF;EACD7B,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,aAAa,CACd;EACDT,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChEC,SAAS,GACR,YAAY;IACX,MAAMO,UAAU,GAAG,IAAAW,qBAAc,EAAC,YAAY,CAAC;IAE/C,OAAO,UAAUyC,MAAM,EAAE/C,GAAG,EAAED,IAAI,EAAE;MAClC,IAAI,CAAC,IAAAI,WAAE,EAAC,0BAA0B,EAAE4C,MAAM,CAAC,EAAE;QAC3CpD,UAAU,CAACI,IAAI,EAAE,IAAI,EAAEA,IAAI,CAAC8C,EAAE,CAAC;MACjC;IACF,CAAC;EACH,CAAC,CAAE;AACX,CAAC,CAAC;AAEFpE,UAAU,CAAC,oBAAoB,EAAE;EAC/BuE,QAAQ,EAAE,qBAAqB;EAC/B1D,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACV;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACD0C,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjCI,EAAE,EAAE;MACFhE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDwC,SAAS,EAAE;MACTjE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEK,MAAM8B,iBAAiB,GAAGA,CAAA,MAAO;EACtCC,cAAc,EAAE;IACdrE,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ,CAAC;EACDA,QAAQ,EAAE;IACRtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;IACpC2B,QAAQ,EAAE;EACZ,CAAC;EACDgC,UAAU,EAAE;IACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;IAClCI,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAACqB,OAAA,CAAAS,iBAAA,GAAAA,iBAAA;AAEHxE,UAAU,CAAC,YAAY,EAAE;EACvB+B,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBnB,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAmC;EAC3EC,OAAO,EAAE,CACP,YAAY,EACZ,mBAAmB,EACnB,aAAa,EACb,MAAM,EACN,cAAc,CACf;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDuD,iBAAiB,CAAC,CAAC;IACtBG,IAAI,EAAE;MACJvE,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA7D,sBAAe,EAAC,QAAQ,CAAC,EACzBC,MAAM,CAACC,MAAM,CACX,UAAUK,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxB,IAAI,CAAC,IAAAqD,0BAAiB,EAACrD,GAAG,EAAE,KAAK,CAAC,EAAE;UAClC,MAAM,IAAIsD,SAAS,CACjB,IAAItD,GAAG,kCACT,CAAC;QACH;MACF,CAAC,EACD;QAAEgC,IAAI,EAAE;MAAS,CACnB,CACF,CAAC,GACD,IAAAzC,sBAAe,EAAC,QAAQ;IAChC;EAAC,EACF;EACDX,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,UAAU4D,MAAM,EAAE/C,GAAG,EAAED,IAAI,EAAE;IAC3B,MAAMyD,KAAK,GAAG,UAAU,CAACC,IAAI,CAACzD,GAAG,CAAC0D,QAAQ,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACF,KAAK,EAAE;IAEZ,MAAM,GAAGG,SAAS,CAAC,GAAGH,KAAK;IAC3B,MAAMI,OAAO,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;IAInC,IAAIF,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI,IAAAxD,WAAE,EAAC,kBAAkB,EAAE4C,MAAM,EAAEa,OAAO,CAAC,EAAE;MAC7C,IAAI,IAAAzD,WAAE,EAAC,0BAA0B,EAAE4C,MAAM,EAAEa,OAAO,CAAC,EAAE;IACvD,CAAC,MAAM,IAAID,SAAS,KAAK,KAAK,EAAE;MAC9B,IAAI,IAAAxD,WAAE,EAAC,UAAU,EAAE4C,MAAM,EAAEa,OAAO,CAAC,EAAE;MACrC,IAAI,IAAAzD,WAAE,EAAC,QAAQ,EAAE4C,MAAM,EAAEa,OAAO,CAAC,EAAE;IACrC,CAAC,MAAM,IAAID,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,IAAAxD,WAAE,EAAC,iBAAiB,EAAE4C,MAAM,CAAC,EAAE;IACrC,CAAC,MAAM,IAAIY,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,IAAAxD,WAAE,EAAC,iBAAiB,EAAE4C,MAAM,EAAE;QAAEe,QAAQ,EAAE/D;MAAK,CAAC,CAAC,EAAE;IACzD,CAAC,MAAM,IAAI4D,SAAS,KAAK,MAAM,EAAE;MAC/B,IAAI,IAAAxD,WAAE,EAAC,cAAc,EAAE4C,MAAM,EAAE;QAAEgB,IAAI,EAAEhE;MAAK,CAAC,CAAC,EAAE;IAClD;IAEA,IAIE,CAAC,IAAAiE,oCAAS,EAACjE,IAAI,CAACqD,IAAI,CAAC,IAAI,IAAAa,yCAAc,EAAClE,IAAI,CAACqD,IAAI,EAAE,KAAK,CAAC,KAGzDrD,IAAI,CAACqD,IAAI,KAAK,MAAM,EACpB;MACA,MAAM,IAAIG,SAAS,CAAC,IAAIxD,IAAI,CAACqD,IAAI,6BAA6B,CAAC;IACjE;EACF,CAAC,GACDhE;AACR,CAAC,CAAC;AAEFX,UAAU,CAAC,aAAa,EAAE;EACxBY,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC;EAC5CC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCX,MAAM,EAAE;IACN8C,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDoB,UAAU,EAAE;MACV7C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDqB,SAAS,EAAE;MACTR,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,kBAAkB,EAAE;EAC7BY,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBX,MAAM,EAAE;IACNuC,KAAK,EAAE;MACLrC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,eAAe,EAAE;EAC1B+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC;EACF,CAAC;EACDF,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3B+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB0D,eAAe,EAAE,eAAe;EAChCvF,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAwE,YAAK,EACb,IAAA7D,sBAAe,EAAC,QAAQ,CAAC,EACzBC,MAAM,CAACC,MAAM,CACX,UAAUK,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxB,IAAI,CAAC,GAAGA,GAAG,GAAG,CAAC,IAAI,CAACkE,MAAM,CAACC,QAAQ,CAACnE,GAAG,CAAC,EAAE;UACxC,MAAMoE,KAAK,GAAG,IAAIC,KAAK,CACrB,uDAAuD,GACrD,6BAA6BrE,GAAG,YACpC,CAAC;UASM,CAIP;QACF;MACF,CAAC,EACD;QAAEgC,IAAI,EAAE;MAAS,CACnB,CACF;IACF;EACF,CAAC;EACD3C,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,aAAa,EAAE;EACxBa,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3B+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS;IACrC;EACF,CAAC;EACDF,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,eAAe,EAAE;EAC1B+B,OAAO,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;EAC7B0D,eAAe,EAAE,cAAc;EAC/B5E,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;EAC7CX,MAAM,EAAE;IACNmB,OAAO,EAAE;MACPjB,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC,CAAC;IACD+E,KAAK,EAAE;MACL1F,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA7D,sBAAe,EAAC,QAAQ,CAAC,EACzBC,MAAM,CAACC,MAAM,CACX,UAAUK,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxB,MAAMuE,OAAO,GAAG,WAAW,CAACf,IAAI,CAACxD,GAAG,CAAC;QACrC,IAAIuE,OAAO,EAAE;UACX,MAAM,IAAIjB,SAAS,CACjB,IAAIiB,OAAO,CAAC,CAAC,CAAC,8BAChB,CAAC;QACH;MACF,CAAC,EACD;QAAEvC,IAAI,EAAE;MAAS,CACnB,CACF,CAAC,GACD,IAAAzC,sBAAe,EAAC,QAAQ,CAAC;MAC/BR,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9B+B,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtCnB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EACjCX,MAAM,EAAE;IACNY,QAAQ,EAAE;MACRV,QAAQ,EAAE,IAAAe,kBAAW,EAAC,GAAG6E,wBAAiB;IAC5C,CAAC;IACDrE,IAAI,EAAE;MACJvB,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDC,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,kBAAkB,EAAE;EAC7B+B,OAAO,EAAE,CACP,QAAQ,EACR,UAAU,EACV,UAAU,EACV,IAAqC,CAACvB,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACpE,CAAC,UAAU,CAAC,GACZ,EAAE,CAAC,CACR;EACDE,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BC,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,aAAa,CAAC;EAC9CX,MAAM,EAAAc,MAAA,CAAAC,MAAA;IACJgF,MAAM,EAAE;MACN7F,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,OAAO;IAChD,CAAC;IACDqE,QAAQ,EAAE;MACR9F,QAAQ,EAAG,YAAY;QACrB,MAAM+F,MAAM,GAAG,IAAAtE,qBAAc,EAAC,YAAY,EAAE,aAAa,CAAC;QAC1D,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMJ,SAAoB,GAAG,SAAAA,CAC3BH,IAAwB,EACxBC,GAAG,EACHC,GAAG,EACH;UACA,MAAMC,SAAoB,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;UAC9D1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;QAEDC,SAAS,CAACU,cAAc,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;QACtE,OAAOV,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACD2D,QAAQ,EAAE;MACR7E,OAAO,EAAE;IACX;EAAC,GACoC,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACpE;IACEgC,QAAQ,EAAE;MACRtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ;EACF,CAAC,GACD,CAAC,CAAC;AAEV,CAAC,CAAC;AAEF1C,UAAU,CAAC,eAAe,EAAE;EAAEuE,QAAQ,EAAE;AAAiB,CAAC,CAAC;AAE3DvE,UAAU,CAAC,SAAS,EAAE;EAGpBY,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/BmB,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;EAC5D7B,MAAM,EAAE;IACNkG,UAAU,EAAE;MACVhG,QAAQ,EAAE,IAAAe,kBAAW,EAAC,QAAQ,EAAE,QAAQ,CAAC;MACzCZ,OAAO,EAAE;IACX,CAAC;IACD8F,WAAW,EAAE;MACXjG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,sBAAsB,CAAC;MAChDtB,OAAO,EAAE,IAAI;MACbmC,QAAQ,EAAE;IACZ,CAAC;IACDL,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClC/B,OAAO,EAAE;IACX,CAAC;IACDgC,IAAI,EAAE,IAAAC,0BAAmB,EAAC,WAAW;EACvC,CAAC;EACD3B,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO;AAC9C,CAAC,CAAC;AAEFb,UAAU,CAAC,kBAAkB,EAAE;EAC7BY,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAE;IACNoG,UAAU,EAAE,IAAA9D,0BAAmB,EAC7B,cAAc,EACd,gBAAgB,EAChB,eACF;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,cAAc,EAAE;EACzB+B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC5EnB,OAAO,EAAE,CACP,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,MAAM,CACP;EACDV,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACD0C,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjCuC,IAAI,EAAAvF,MAAA,CAAAC,MAAA;MACFb,QAAQ,EAAE,IAAAe,kBAAW,EAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;IAAC,GACR,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACpE;MAAEH,OAAO,EAAE;IAAS,CAAC,GACrB,CAAC,CAAC,CACP;IACD6E,QAAQ,EAAE;MACR7E,OAAO,EAAE;IACX,CAAC;IACDgB,GAAG,EAAE;MACHnB,QAAQ,EAAG,YAAY;QACrB,MAAM+F,MAAM,GAAG,IAAAtE,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eACF,CAAC;QACD,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMJ,SAAoB,GAAG,SAAAA,CAAUH,IAAoB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACrE,MAAMC,SAAS,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;UACnD1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;QAEDC,SAAS,CAACU,cAAc,GAAG,CACzB,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,CAChB;QACD,OAAOV,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDiD,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC,EACF;EACDhB,OAAO,EAAE,CACP,mBAAmB,EACnB,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,cAAc;AAElB,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3B+B,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,UAAU,EACV,WAAW,EACX,IAAqC,CAACvB,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACpE,CAAC,YAAY,CAAC,GACd,EAAE,CAAC,CACR;EACDR,MAAM,EAAE;IACNkF,QAAQ,EAAE;MACR7E,OAAO,EAAE;IACX,CAAC;IACDgB,GAAG,EAAE;MACHnB,QAAQ,EAAG,YAAY;QACrB,MAAM+F,MAAM,GAQR,IAAAtE,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EAEf,gBAAgB,EAChB,aACF,CAAC;QACL,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMJ,SAAoB,GAAGT,MAAM,CAACC,MAAM,CACxC,UAAUK,IAAsB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAC1C,MAAMC,SAAS,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;UACnD1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EACD;UAEEW,cAAc,EASV,CACE,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,aAAa;QAErB,CACF,CAAC;QACD,OAAOV,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDW,KAAK,EAAE;MAGLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,aAAa;IACtD,CAAC;IACD2E,SAAS,EAAE;MACTpG,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA7D,sBAAe,EAAC,SAAS,CAAC,EAC1BC,MAAM,CAACC,MAAM,CACX,UAAUK,IAAsB,EAAEC,GAAG,EAAEiF,SAAS,EAAE;QAChD,IAAI,CAACA,SAAS,EAAE;QAEhB,IAAIlF,IAAI,CAAC8D,QAAQ,EAAE;UACjB,MAAM,IAAIN,SAAS,CACjB,yEACF,CAAC;QACH;QAEA,IAAI,CAAC,IAAApD,WAAE,EAAC,YAAY,EAAEJ,IAAI,CAACC,GAAG,CAAC,EAAE;UAC/B,MAAM,IAAIuD,SAAS,CACjB,iFACF,CAAC;QACH;MACF,CAAC,EACD;QAAEtB,IAAI,EAAE;MAAU,CACpB,CACF,CAAC,GACD,IAAAzC,sBAAe,EAAC,SAAS,CAAC;MAChCR,OAAO,EAAE;IACX,CAAC;IACDmE,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ;EACF,CAAC;EACD9B,OAAO,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC;EACvCC,OAAO,EAAE,CAAC,mBAAmB,EAAE,UAAU,EAAE,cAAc,CAAC;EAC1DT,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChEC,SAAS,GACR,YAAY;IACX,MAAMU,OAAO,GAAG,IAAAQ,qBAAc,EAC5B,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,uBAAuB,EACvB,qBAAqB,EACrB,iBACF,CAAC;IACD,MAAMI,UAAU,GAAG,IAAAJ,qBAAc,EAAC,YAAY,CAAC;IAE/C,OAAO,UAAUyC,MAAM,EAAE/C,GAAG,EAAED,IAAI,EAAE;MAClC,MAAMG,SAAS,GAAG,IAAAC,WAAE,EAAC,eAAe,EAAE4C,MAAM,CAAC,GACzCjD,OAAO,GACPY,UAAU;MACdR,SAAS,CAACH,IAAI,EAAE,OAAO,EAAEA,IAAI,CAACc,KAAK,CAAC;IACtC,CAAC;EACH,CAAC,CAAE;AACX,CAAC,CAAC;AAEFpC,UAAU,CAAC,aAAa,EAAE;EACxBY,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;EACvCmB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBlB,OAAO,EAEH,CAAC,mBAAmB,EAAE,aAAa,EAAE,MAAM,CAAC;EAChD4E,eAAe,EAAE,cAAc;EAC/BvF,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDuD,iBAAiB,CAAC,CAAC;IACtBiC,QAAQ,EAAE;MACRrG,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAmB,qBAAc,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBAAqB,EAErB,aAAa,EACb,mBACF,CAAC,GACD,IAAAA,qBAAc,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACR;EAAC,EACF;EACDzB,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,UAAU4D,MAAwC,EAAE/C,GAAG,EAAE;IACvD,MAAMwD,KAAK,GAAG,gBAAgB,CAACC,IAAI,CAACzD,GAAG,CAAC0D,QAAQ,CAAC,CAAC,CAAC;IACnD,IAAI,CAACF,KAAK,EAAE,MAAM,IAAIc,KAAK,CAAC,sCAAsC,CAAC;IAEnE,MAAM,GAAGa,OAAO,EAAEC,KAAK,CAAC,GAAG5B,KAI1B;IACD,IAAKT,MAAM,CAACoC,OAAO,CAAC,CAAcE,MAAM,GAAG,CAACD,KAAK,GAAG,CAAC,EAAE;MACrD,MAAM,IAAI7B,SAAS,CACjB,uCAAuC4B,OAAO,EAChD,CAAC;IACH;EACF,CAAC,GACD/F;AACR,CAAC,CAAC;AAEFX,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EAC/DX,MAAM,EAAE;IACNuG,QAAQ,EAAE;MACRrG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,oBAAoB,EAAE;EAC/BY,OAAO,EAAE,CAAC,aAAa,CAAC;EACxBV,MAAM,EAAE;IACN2G,WAAW,EAAE,IAAArE,0BAAmB,EAAC,YAAY;EAC/C,CAAC;EACD3B,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFb,UAAU,CAAC,yBAAyB,EAAE;EACpCY,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,EAAE,mBAAmB,CAAC;EAC5CX,MAAM,EAAE;IACN+B,UAAU,EAAE;MACV7B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,YAAY,EAAE;EACvBY,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC/BV,MAAM,EAAE;IACN8C,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDO,UAAU,EAAE,IAAAT,0BAAmB,EAAC,WAAW;EAC7C;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC;EAClCC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;EACjDX,MAAM,EAAE;IACN4G,YAAY,EAAE;MACZ1G,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDkF,KAAK,EAAE,IAAAvE,0BAAmB,EAAC,YAAY;EACzC;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,gBAAgB,EAAE;EAC3Ba,OAAO,EAEH,CAAC,YAAY;AACnB,CAAC,CAAC;AAEFb,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EAC/DX,MAAM,EAAE;IACNuG,QAAQ,EAAE;MACRrG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,cAAc,EAAE;EACzBY,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;EAC1CC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBX,MAAM,EAAE;IACN8G,KAAK,EAAE;MACL5G,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA/C,qBAAc,EAAC,gBAAgB,CAAC,EAChCb,MAAM,CAACC,MAAM,CACX,UAAUK,IAAoB,EAAE;QAI9B,IAAI,CAACA,IAAI,CAAC2F,OAAO,IAAI,CAAC3F,IAAI,CAAC4F,SAAS,EAAE;UACpC,MAAM,IAAIpC,SAAS,CACjB,6DACF,CAAC;QACH;MACF,CAAC,EACD;QAAE3C,cAAc,EAAE,CAAC,gBAAgB;MAAE,CACvC,CACF,CAAC,GACD,IAAAN,qBAAc,EAAC,gBAAgB;IACvC,CAAC;IACDoF,OAAO,EAAE;MACPvE,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDqF,SAAS,EAAE;MACTxE,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,iBAAiB,EAAE;EAC5B+B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC3C7B,MAAM,EAAE;IACNiH,MAAM,EAAE;MACN5G,OAAO,EAAE;IACX,CAAC;IACDkG,QAAQ,EAAE;MACRrG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDf,QAAQ,EAAE;MACRV,QAAQ,EAAE,IAAAe,kBAAW,EAAC,GAAGiG,sBAAe;IAC1C;EACF,CAAC;EACDxG,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY;AACrC,CAAC,CAAC;AAEFb,UAAU,CAAC,kBAAkB,EAAE;EAC7B+B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC3C7B,MAAM,EAAE;IACNiH,MAAM,EAAE;MACN5G,OAAO,EAAE;IACX,CAAC;IACDkG,QAAQ,EAAE;MACRrG,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAmB,qBAAc,EAAC,YAAY,CAAC,GAC5B,IAAAA,qBAAc,EAAC,YAAY,EAAE,kBAAkB;IACvD,CAAC;IACDf,QAAQ,EAAE;MACRV,QAAQ,EAAE,IAAAe,kBAAW,EAAC,GAAGkG,uBAAgB;IAC3C;EACF,CAAC;EACDzG,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFb,UAAU,CAAC,qBAAqB,EAAE;EAChC+B,OAAO,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;EACjCnB,OAAO,EAAE,CAAC,cAAc,CAAC;EACzBC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCX,MAAM,EAAE;IACNiE,OAAO,EAAE;MACP/D,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACD6D,IAAI,EAAE;MACJnG,QAAQ,EAAE,IAAAe,kBAAW,EACnB,KAAK,EACL,KAAK,EACL,OAAO,EAEP,OAAO,EAEP,aACF;IACF,CAAC;IACDmG,YAAY,EAAE,IAAA9E,0BAAmB,EAAC,oBAAoB;EACxD,CAAC;EACDpC,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,CAAC,MAAM;IACL,MAAM6G,WAAW,GAAG,IAAA1F,qBAAc,EAAC,YAAY,EAAE,aAAa,CAAC;IAC/D,MAAM2F,eAAe,GAAG,IAAA3F,qBAAc,EACpC,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aACF,CAAC;IACD,MAAM4F,iBAAiB,GAAG,IAAA5F,qBAAc,EACtC,YAAY,EACZ,aAAa,EACb,aACF,CAAC;IAED,OAAO,UAAUyC,MAAM,EAAE/C,GAAG,EAAED,IAA2B,EAAE;MACzD,MAAM;QAAEiF,IAAI;QAAEe;MAAa,CAAC,GAAGhG,IAAI;MACnC,MAAMoG,YAAY,GAAG,IAAAhG,WAAE,EAAC,eAAe,EAAE4C,MAAM,EAAE;QAAE3C,IAAI,EAAEL;MAAK,CAAC,CAAC;MAChE,IAAIoG,YAAY,EAAE;QAChB,IAAIJ,YAAY,CAACV,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAM,IAAI9B,SAAS,CACjB,8EAA8ER,MAAM,CAACd,IAAI,EAC3F,CAAC;QACH;MACF;MACA,KAAK,MAAMmE,IAAI,IAAIL,YAAY,EAAE;QAC/B,IAAIf,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;UACxD,IAAI,CAACmB,YAAY,IAAI,CAACC,IAAI,CAAClE,IAAI,EAAE;YAC/B8D,WAAW,CAACI,IAAI,EAAE,IAAI,EAAEA,IAAI,CAACvD,EAAE,CAAC;UAClC,CAAC,MAAM;YACLoD,eAAe,CAACG,IAAI,EAAE,IAAI,EAAEA,IAAI,CAACvD,EAAE,CAAC;UACtC;QACF,CAAC,MAAM;UACLqD,iBAAiB,CAACE,IAAI,EAAE,IAAI,EAAEA,IAAI,CAACvD,EAAE,CAAC;QACxC;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAAC,GACJzD;AACR,CAAC,CAAC;AAEFX,UAAU,CAAC,oBAAoB,EAAE;EAC/BY,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EACvBV,MAAM,EAAE;IACNkE,EAAE,EAAE;MACFhE,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAmB,qBAAc,EAAC,MAAM,EAAE,aAAa,CAAC,GACrC,IAAAA,qBAAc,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aACF;IACR,CAAC;IACD+F,QAAQ,EAAE;MACRlF,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS;IACrC,CAAC;IACD0C,IAAI,EAAE;MACJf,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACzBC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;EAClEX,MAAM,EAAE;IACN8C,IAAI,EAAE;MACJ5C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,eAAe,EAAE;EAC1BY,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC3BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBX,MAAM,EAAE;IACN+F,MAAM,EAAE;MACN7F,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAGF7B,UAAU,CAAC,mBAAmB,EAAE;EAC9BY,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAmC;EAC1EmB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BlB,OAAO,EAEH,CAAC,mBAAmB,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAC3DX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDuD,iBAAiB,CAAC,CAAC;IACtB7C,IAAI,EAAE;MACJvB,QAAQ,EAAE,IAAAyB,qBAAc,EACtB,YAAY,EACZ,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACF,CAAC;IACDC,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IAED6C,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEF1C,UAAU,CAAC,cAAc,EAAE;EACzBY,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;EACvCmB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBlB,OAAO,EAAE,CAAC,mBAAmB,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAChEX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDuD,iBAAiB,CAAC,CAAC;IACtBrE,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAwE,YAAK,EACb,IAAA7D,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAuC,iBAAU,EAAC,IAAAhD,4BAAqB,EAAC,MAAM,EAAE,aAAa,CAAC,CACzD;IACF;EAAC;AAEL,CAAC,CAAC;AAEFN,UAAU,CAAC,yBAAyB,EAAE;EACpC+B,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;EACpCnB,OAAO,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC;EACxEC,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACV;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACD0C,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjC/B,UAAU,EAAE;MAEV7B,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS;IACrC,CAAC;IACDwB,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB,EAAE,YAAY;IACzD,CAAC;IACDwC,SAAS,EAAE;MACTjE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEF1C,UAAU,CAAC,WAAW,EAAE;EACtBY,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBV,MAAM,EAAE;IACNqC,IAAI,EAAE,IAAAC,0BAAmB,EACvB,aAAa,EACb,oBAAoB,EACpB,eAAe,EACf,sBAAsB,EACtB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,aACF;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,iBAAiB,EAAE;EAC5B+B,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,CAAC;EACnDnB,OAAO,EAAE,CACP,YAAY,EACZ,IAAI,EACJ,gBAAgB,EAChB,YAAY,EAC0C,qBAAqB,EAC3E,QAAQ,EACR,YAAY,EACZ,MAAM,CACP;EACDC,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC;EAC5CX,MAAM,EAAE;IACNkE,EAAE,EAAE;MACFhE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACd1C,QAAQ,EAKJ,IAAAyB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDgG,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACD,CAEI,qBAAqB,GAAG;MAC1BzB,QAAQ,EAAE,IAAAyB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACDoF,UAAU,EAAE;MACV1H,QAAQ,EAAE,IAAAkC,kBAAW,EAIf,+BAA+B,EACnC,iBACF,CAAC;MACDI,QAAQ,EAAE;IACZ,CAAC;IACDgC,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACDqF,MAAM,EAAE;MACN3H,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,kBAAkB,CAAC;MAC5Ca,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,kBAAkB,EAAE;EAC7BuE,QAAQ,EAAE,iBAAiB;EAC3B1D,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC;EAC1DX,MAAM,EAAE;IACNkE,EAAE,EAAE;MACFhE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MAGtCa,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACd1C,QAAQ,EAKJ,IAAAyB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDH,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDgG,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACD,CAEI,qBAAqB,GAAG;MAC1BzB,QAAQ,EAAE,IAAAyB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACDoF,UAAU,EAAE;MACV1H,QAAQ,EAAE,IAAAkC,kBAAW,EAIf,+BAA+B,EACnC,iBACF,CAAC;MACDI,QAAQ,EAAE;IACZ,CAAC;IACDgC,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACDqF,MAAM,EAAE;MACN3H,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,kBAAkB,CAAC;MAC5Ca,QAAQ,EAAE;IACZ,CAAC;IACDyB,OAAO,EAAE;MACP/D,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDsF,QAAQ,EAAE;MACR5H,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ;EACF,CAAC;EACDtC,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChEC,SAAS,GACR,YAAY;IACX,MAAMO,UAAU,GAAG,IAAAW,qBAAc,EAAC,YAAY,CAAC;IAC/C,OAAO,UAAUyC,MAAM,EAAE/C,GAAG,EAAED,IAAI,EAAE;MAClC,IAAI,CAAC,IAAAI,WAAE,EAAC,0BAA0B,EAAE4C,MAAM,CAAC,EAAE;QAC3CpD,UAAU,CAACI,IAAI,EAAE,IAAI,EAAEA,IAAI,CAAC8C,EAAE,CAAC;MACjC;IACF,CAAC;EACH,CAAC,CAAE;AACX,CAAC,CAAC;AAEK,MAAM6D,gBAAgB,GAAAlE,OAAA,CAAAkE,gBAAA,GAAG;EAC9BC,UAAU,EAAE;IACVxF,QAAQ,EAAE,IAAI;IACdtC,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,iBAAiB;EACzC,CAAC;EACD6F,UAAU,EAAE;IACVC,UAAU,EAAE,IAAI;IAChB1F,QAAQ,EAAE,IAAI;IACdtC,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,iBAAiB;EACzC;AACF,CAAC;AAEDtC,UAAU,CAAC,sBAAsB,EAAE;EACjC+B,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBnB,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EAC/CC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA;IACJoH,MAAM,EAAE;MACNjI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACDyG,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAApH,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;EAAC,GACvD8G,gBAAgB;AAEvB,CAAC,CAAC;AAEFjI,UAAU,CAAC,0BAA0B,EAAE;EACrCY,OAAO,EAAE,CAAC,aAAa,CAAC;EACxBC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDX,MAAM,EAAE;IACNsI,WAAW,EAAE,IAAAC,mBAAY,EACvB,mBAAmB,EACnB,qBAAqB,EACrB,kBAAkB,EAClB,YACF,CAAC;IACDH,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAApH,kBAAW,EAAC,OAAO,CAAC;EACnD;AACF,CAAC,CAAC;AAEFnB,UAAU,CAAC,wBAAwB,EAAE;EACnC+B,OAAO,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC;EAChDnB,OAAO,EAEH,CAAC,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EACvEC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA;IACJuH,WAAW,EAAE;MACX9F,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA/C,qBAAc,EAAC,aAAa,CAAC,EAC7Bb,MAAM,CAACC,MAAM,CACX,UAAUK,IAA8B,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAIlD,IAAIA,GAAG,IAAIF,IAAI,CAACoH,UAAU,CAAC9B,MAAM,EAAE;UACjC,MAAM,IAAI9B,SAAS,CACjB,qEACF,CAAC;QACH;QAKA,IAAItD,GAAG,IAAIF,IAAI,CAAC+G,MAAM,EAAE;UACtB,MAAM,IAAIvD,SAAS,CACjB,2CACF,CAAC;QACH;MACF,CAAC,EACD;QAAE3C,cAAc,EAAE,CAAC,aAAa;MAAE,CACpC,CACF,CAAC,GACD,IAAAN,qBAAc,EAAC,aAAa;IACpC;EAAC,GACEoG,gBAAgB;IACnBS,UAAU,EAAE;MACVnI,OAAO,EAAE,EAAE;MACXH,QAAQ,EAAE,IAAAC,cAAO,EACd,YAAY;QACX,MAAMsI,OAAO,GAAG,IAAA9G,qBAAc,EAC5B,iBAAiB,EACjB,wBAAwB,EACxB,0BACF,CAAC;QACD,MAAM+G,UAAU,GAAG,IAAA/G,qBAAc,EAAC,iBAAiB,CAAC;QAEpD,IAEE,CAACrB,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAEnC,OAAOiI,OAAO;QAEhB,OAAO3H,MAAM,CAACC,MAAM,CAClB,UAAUK,IAA8B,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAClD,MAAMC,SAAS,GAAGH,IAAI,CAAC+G,MAAM,GAAGM,OAAO,GAAGC,UAAU;UACpDnH,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EACD;UACEW,cAAc,EAAE,CACd,iBAAiB,EACjB,wBAAwB,EACxB,0BAA0B;QAE9B,CACF,CAAC;MACH,CAAC,CAAE,CACL;IACF,CAAC;IACDkG,MAAM,EAAE;MACNjI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,eAAe,CAAC;MACzCa,QAAQ,EAAE;IACZ,CAAC;IACD4F,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAApH,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;EAAC;AAE9D,CAAC,CAAC;AAEFnB,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC9BC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BX,MAAM,EAAE;IACN2I,KAAK,EAAE;MACLzI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDiH,QAAQ,EAAE;MACR1I,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACDyG,UAAU,EAAE;MAEVlI,QAAQ,EAAE,IAAAe,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;MACtCuB,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,gBAAgB,EAAE;EAC3BY,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAClCmB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAC3ClB,OAAO,EAAE,CACP,UAAU,EACV,WAAW,EACX,KAAK,EACL,aAAa,EACb,MAAM,EACN,eAAe,CAChB;EACDX,MAAM,EAAE;IACNyB,IAAI,EAAE;MACJvB,QAAQ,EAAG,YAAY;QACrB,IAEE,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,EACnC;UACA,OAAO,IAAAmB,qBAAc,EAAC,qBAAqB,EAAE,MAAM,CAAC;QACtD;QAEA,MAAM2G,WAAW,GAAG,IAAA3G,qBAAc,EAAC,qBAAqB,CAAC;QACzD,MAAMkH,IAAI,GAAG,IAAAlH,qBAAc,EACzB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF,CAAC;QAED,OAAOb,MAAM,CAACC,MAAM,CAClB,UAAUK,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACxB,IAAI,IAAAE,WAAE,EAAC,qBAAqB,EAAEF,GAAG,CAAC,EAAE;YAClCgH,WAAW,CAAClH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;UAC7B,CAAC,MAAM;YACLuH,IAAI,CAACzH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;UACtB;QACF,CAAC,EACD;UACEW,cAAc,EAAE,CACd,qBAAqB,EACrB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBAAqB;QAEzB,CACF,CAAC;MACH,CAAC,CAAE;IACL,CAAC;IACDL,KAAK,EAAE;MACL1B,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDmH,KAAK,EAAE;MACLzI,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9B+B,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;EACjCnB,OAAO,EAEH,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EACxDC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,2BAA2B,CAAC;EAClEX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDgH,gBAAgB;IACnBgB,MAAM,EAAE;MACNvG,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS;IACrC,CAAC;IACDmI,KAAK,EAAE;MACL3I,OAAO,EAAE,IAAI;MACbH,QAAQ,EAAE,IAAAe,kBAAW,EAAC,QAAQ,EAAE,OAAO;IACzC,CAAC;IACDuH,UAAU,EAAE,IAAAlG,0BAAmB,EAC7B,iBAAiB,EACjB,wBAAwB,EACxB,0BACF,CAAC;IACD6F,MAAM,EAAE;MACNjI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACDsH,UAAU,EAAE;MAGV/I,QAAQ,EAAE,IAAAe,kBAAW,EAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;MAChDuB,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEF1C,UAAU,CAAC,wBAAwB,EAAE;EACnCY,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BX,MAAM,EAAE;IACN2I,KAAK,EAAE;MACLzI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,0BAA0B,EAAE;EACrCY,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BX,MAAM,EAAE;IACN2I,KAAK,EAAE;MACLzI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAC9BmB,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC9BlB,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BX,MAAM,EAAE;IACN2I,KAAK,EAAE;MACLzI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDwD,QAAQ,EAAE;MACRjF,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACDsH,UAAU,EAAE;MAGV/I,QAAQ,EAAE,IAAAe,kBAAW,EAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;MAChDuB,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,kBAAkB,EAAE;EAC7BY,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;EAC9BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAE;IACNgJ,KAAK,EAAE;MACL3I,OAAO,EAAE,IAAI;MACbH,QAAQ,EAAE,IAAAe,kBAAW,EAAC,QAAQ,EAAE,OAAO;IACzC,CAAC;IACDkH,MAAM,EAAE;MACNjI,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDuH,OAAO,EAAE;MACPhJ,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,cAAc,EAAE;EACzBY,OAAO,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAE;IACNoF,IAAI,EAAE;MACJlF,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA/C,qBAAc,EAAC,YAAY,CAAC,EAC5Bb,MAAM,CAACC,MAAM,CACX,UAAUK,IAAoB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxC,IAAI0E,QAAQ;QACZ,QAAQ1E,GAAG,CAACmD,IAAI;UACd,KAAK,UAAU;YACbuB,QAAQ,GAAG,MAAM;YACjB;UACF,KAAK,KAAK;YACRA,QAAQ,GAAG,QAAQ;YACnB;UACF,KAAK,QAAQ;YACXA,QAAQ,GAAG,MAAM;YACjB;QACJ;QACA,IAAI,CAAC,IAAAxE,WAAE,EAAC,YAAY,EAAEJ,IAAI,CAAC4E,QAAQ,EAAE;UAAEvB,IAAI,EAAEuB;QAAS,CAAC,CAAC,EAAE;UACxD,MAAM,IAAIpB,SAAS,CAAC,2BAA2B,CAAC;QAClD;MACF,CAAC,EACD;QAAE3C,cAAc,EAAE,CAAC,YAAY;MAAE,CACnC,CACF,CAAC,GACD,IAAAN,qBAAc,EAAC,YAAY;IACnC,CAAC;IACDqE,QAAQ,EAAE;MACR9F,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEK,MAAMwH,2BAA2B,GAAGA,CAAA,MAAO;EAChDrB,QAAQ,EAAE;IACR5H,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;IACpC2B,QAAQ,EAAE;EACZ,CAAC;EACD4G,aAAa,EAAE;IACblJ,QAAQ,EAAE,IAAAe,kBAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;IACvDuB,QAAQ,EAAE;EACZ,CAAC;EACD6G,MAAM,EAAE;IACNhJ,OAAO,EAAE;EACX,CAAC;EACDiJ,QAAQ,EAAE;IACRjJ,OAAO,EAAE;EACX,CAAC;EACD6E,QAAQ,EAAE;IACR7E,OAAO,EAAE;EACX,CAAC;EACDmC,QAAQ,EAAE;IACRtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;IACpC2B,QAAQ,EAAE;EACZ,CAAC;EACDnB,GAAG,EAAE;IACHnB,QAAQ,EAAE,IAAAwE,YAAK,EACZ,YAAY;MACX,MAAMuB,MAAM,GAAG,IAAAtE,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eACF,CAAC;MACD,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;MAE7C,OAAO,UAAUP,IAAS,EAAEC,GAAW,EAAEC,GAAQ,EAAE;QACjD,MAAMC,SAAS,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;QACnD1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;MAC3B,CAAC;IACH,CAAC,CAAE,CAAC,EACJ,IAAAK,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,YACF,CACF;EACF;AACF,CAAC,CAAC;AAACkC,OAAA,CAAAsF,2BAAA,GAAAA,2BAAA;AAEI,MAAMI,gCAAgC,GAAGA,CAAA,KAAAzI,MAAA,CAAAC,MAAA,KAC3C0C,cAAc,CAAC,CAAC,EAChB0F,2BAA2B,CAAC,CAAC;EAChCzF,MAAM,EAAE,IAAApB,0BAAmB,EAAC,mBAAmB,EAAE,qBAAqB,CAAC;EACvE+D,IAAI,EAAE;IACJnG,QAAQ,EAAE,IAAAe,kBAAW,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC5DZ,OAAO,EAAE;EACX,CAAC;EACDmJ,MAAM,EAAE;IACNtJ,QAAQ,EAAE,IAAAwE,YAAK,EACb,IAAA7D,sBAAe,EAAC,QAAQ,CAAC,EACzB,IAAAI,kBAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAC9C,CAAC;IACDuB,QAAQ,EAAE;EACZ,CAAC;EACDgC,UAAU,EAAE;IACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;IAClCI,QAAQ,EAAE;EACZ;AAAC,EACD;AAACqB,OAAA,CAAA0F,gCAAA,GAAAA,gCAAA;AAEHzJ,UAAU,CAAC,aAAa,EAAE;EACxBa,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,CAAC;EAC5EkB,OAAO,EAAE,CACP,MAAM,EACN,KAAK,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,CACR;EACDnB,OAAO,EAAE,CACP,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,MAAM,CACP;EACDV,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDwI,gCAAgC,CAAC,CAAC,EAClCzF,4BAA4B,CAAC,CAAC;IACjCzB,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC;AAEL,CAAC,CAAC;AAEF7B,UAAU,CAAC,eAAe,EAAE;EAC1BY,OAAO,EAAE,CACP,YAAY,EACZ,YAAY,EACZ,gBAAgB,CACjB;EACDmB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBlB,OAAO,EAAE,CAAC,mBAAmB,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAChEX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDuD,iBAAiB,CAAC,CAAC;IACtB8B,UAAU,EAAE,IAAA9D,0BAAmB,EAAC,aAAa,EAAE,gBAAgB;EAAC;AAEpE,CAAC,CAAC;AAEFxC,UAAU,CAAC,eAAe,EAAE;EAC1BY,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtB4E,eAAe,EAAE,gBAAgB;EACjCvF,MAAM,EAAE;IACNuG,QAAQ,EAAE;MACRrG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CACR,OAAO,EAGH;EACEa,OAAO,EAAE,CAAC,YAAY;AACxB,CACN,CAAC;AAEDb,UAAU,CAAC,0BAA0B,EAAE;EACrCY,OAAO,EAEH,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC;EACtCmB,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;EACzBlB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAE;IACNyJ,GAAG,EAAE;MACHvJ,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACD+H,KAAK,EAAE;MACLxJ,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,iBAAiB;IAC5C,CAAC;IACD,CAAkD,gBAAgB,GAAG;MACnEzB,QAAQ,EAAE,IAAAyB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,iBAAiB,EAAE;EAC5B+B,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1B7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAwE,YAAK,EACb,IAAAiF,kBAAW,EAAC;QACVC,GAAG,EAAE;UACH1J,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;QACpC,CAAC;QACDgJ,MAAM,EAAE;UACN3J,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ,CAAC;UACnC2B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,EACF,SAASsH,8BAA8BA,CAAC1I,IAAuB,EAAE;QAC/D,MAAMwI,GAAG,GAAGxI,IAAI,CAACc,KAAK,CAAC0H,GAAG;QAE1B,IAAIG,kBAAkB,GAAG,KAAK;QAE9B,MAAMrE,KAAK,GAAGA,CAAA,KAAM;UAElB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;QACjD,CAAC;QACD,MAAM;UAAEqE,GAAG;UAAEC;QAAgB,CAAC,GAAG,IAAAC,sCAAkB,EACjD,UAAU,EACVN,GAAG,EACH,CAAC,EACD,CAAC,EACD,CAAC,EACD;UACEO,YAAYA,CAAA,EAAG;YACbJ,kBAAkB,GAAG,IAAI;UAC3B,CAAC;UACDK,mBAAmB,EAAE1E,KAAK;UAC1B2E,qBAAqB,EAAE3E,KAAK;UAC5B4E,gCAAgC,EAAE5E,KAAK;UACvC6E,0BAA0B,EAAE7E,KAAK;UACjC8E,YAAY,EAAE9E,KAAK;UACnB+E,gBAAgB,EAAE/E;QACpB,CACF,CAAC;QACD,IAAI,CAACqE,kBAAkB,EAAE,MAAM,IAAIpE,KAAK,CAAC,aAAa,CAAC;QAEvDvE,IAAI,CAACc,KAAK,CAAC2H,MAAM,GAAGI,eAAe,GAAG,IAAI,GAAGD,GAAG;MAClD,CACF;IACF,CAAC;IACDU,IAAI,EAAE;MACJrK,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;EAClCC,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;EAClCX,MAAM,EAAE;IACN2K,MAAM,EAAE,IAAArI,0BAAmB,EAAC,iBAAiB,CAAC;IAC9CqE,WAAW,EAAE;MACXzG,QAAQ,EAAE,IAAAwE,YAAK,EACb,IAAA7D,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAuC,iBAAU,EACR,IAAAzB,qBAAc,EACZ,YAAY,EAEZ,QACF,CACF,CAAC,EACD,UAAUP,IAAuB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC3C,IAAIF,IAAI,CAACuJ,MAAM,CAACjE,MAAM,KAAKpF,GAAG,CAACoF,MAAM,GAAG,CAAC,EAAE;UACzC,MAAM,IAAI9B,SAAS,CACjB,aACExD,IAAI,CAACkC,IAAI,gFAEThC,GAAG,CAACoF,MAAM,GAAG,CAAC,mBACGtF,IAAI,CAACuJ,MAAM,CAACjE,MAAM,EACvC,CAAC;QACH;MACF,CACF;IACF;EACF;AACF,CAAC,CAAC;AAEF5G,UAAU,CAAC,iBAAiB,EAAE;EAC5B+B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACjCnB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCX,MAAM,EAAE;IACN4K,QAAQ,EAAE;MACR1K,QAAQ,EAC0BI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9D,IAAAkE,YAAK,EACH,IAAA7D,sBAAe,EAAC,SAAS,CAAC,EAC1BC,MAAM,CAACC,MAAM,CACX,UAAUK,IAAuB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC3C,IAAIA,GAAG,IAAI,CAACF,IAAI,CAACmF,QAAQ,EAAE;UACzB,MAAM,IAAI3B,SAAS,CACjB,6EACF,CAAC;QACH;MACF,CAAC,EACD;QAAEtB,IAAI,EAAE;MAAU,CACpB,CACF,CAAC,GACD,IAAAzC,sBAAe,EAAC,SAAS,CAAC;MAChCR,OAAO,EAAE;IACX,CAAC;IACDkG,QAAQ,EAAE;MACR/D,QAAQ,EAAE,IAAI;MACdtC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAGF7B,UAAU,CAAC,iBAAiB,EAAE;EAC5B+B,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBnB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCX,MAAM,EAAE;IACNuG,QAAQ,EAAE;MACRrG,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAGF7B,UAAU,CAAC,QAAQ,EAAE;EACnBa,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAGFb,UAAU,CAAC,eAAe,EAAE;EAC1B+B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB7B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAEJ,IAAAW,sBAAe,EAAC,QAAQ;IAC9B;EACF,CAAC;EACDF,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFb,UAAU,CAAC,0BAA0B,EAAE;EACrCY,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BX,MAAM,EAAE;IACN4I,QAAQ,EAAE;MACR1I,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,0BAA0B,EAAE;EACrC+B,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACvDnB,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BC,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/BX,MAAM,EAAE;IACN+F,MAAM,EAAE;MACN7F,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDqE,QAAQ,EAAE;MACR9F,QAAQ,EAAG,YAAY;QACrB,MAAM+F,MAAM,GAAG,IAAAtE,qBAAc,EAAC,YAAY,CAAC;QAC3C,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMJ,SAAoB,GAAGT,MAAM,CAACC,MAAM,CACxC,UAAUK,IAAgC,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACpD,MAAMC,SAAS,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;UACnD1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EAED;UAAEW,cAAc,EAAE,CAAC,YAAY,EAAE,YAAY;QAAE,CACjD,CAAC;QACD,OAAOV,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACD2D,QAAQ,EAAE;MACR7E,OAAO,EAAE;IACX,CAAC;IACDmC,QAAQ,EAAE;MACRtC,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAK,sBAAe,EAAC,SAAS,CAAC,GAC1B,IAAA6D,YAAK,EAAC,IAAA7D,sBAAe,EAAC,SAAS,CAAC,EAAE,IAAAgK,+BAAwB,EAAC,CAAC;IACpE;EACF;AACF,CAAC,CAAC;AAEF/K,UAAU,CAAC,wBAAwB,EAAE;EACnCY,OAAO,EAEH,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,CAAC;EAC9DmB,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;EAC5ClB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBX,MAAM,EAAAc,MAAA,CAAAC,MAAA;IACJ0B,MAAM,EAAE;MACNvC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDe,SAAS,EAAE,IAAAJ,0BAAmB,EAC5B,YAAY,EACZ,eAAe,EACf,qBACF,CAAC;IACDE,QAAQ,EAAE;MACRtC,QAAQ,EAC2B,CAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE,IAAAK,sBAAe,EAAC,SAAS,CAAC,GAC1B,IAAA6D,YAAK,EAAC,IAAA7D,sBAAe,EAAC,SAAS,CAAC,EAAE,IAAAgK,+BAAwB,EAAC,CAAC;IACpE,CAAC;IACDlI,aAAa,EAAE;MACbzC,QAAQ,EAKJ,IAAAyB,qBAAc,EAAC,4BAA4B,CAAC;MAChDa,QAAQ,EAAE;IACZ;EAAC,GAGG;IACEI,cAAc,EAAE;MACd1C,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,8BAA8B,CAAC;MACxDa,QAAQ,EAAE;IACZ;EACF,CAAC;AAET,CAAC,CAAC;AAGF1C,UAAU,CAAC,eAAe,EAAE;EAC1BY,OAAO,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC;EACrEmB,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,QAAQ,CACT;EACDlB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDoI,2BAA2B,CAAC,CAAC;IAChCjH,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDkF,QAAQ,EAAE;MACRxH,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACD+B,cAAc,EAAE;MACdrE,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDgC,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACDsI,QAAQ,EAAE;MACR5K,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDyB,OAAO,EAAE;MACP/D,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDuI,QAAQ,EAAE;MACR7K,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEF1C,UAAU,CAAC,uBAAuB,EAAE;EAClCY,OAAO,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC;EACzDmB,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,QAAQ,CACT;EACDlB,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACjCX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDoI,2BAA2B,CAAC,CAAC;IAChC9H,GAAG,EAAE;MACHnB,QAAQ,EAAE,IAAAwE,YAAK,EACZ,YAAY;QACX,MAAMuB,MAAM,GAAG,IAAAtE,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,aACF,CAAC;QACD,MAAMuD,QAAQ,GAAG,IAAAvD,qBAAc,EAAC,YAAY,CAAC;QAE7C,OAAO,UAAUP,IAAS,EAAEC,GAAW,EAAEC,GAAQ,EAAE;UACjD,MAAMC,SAAS,GAAGH,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ,GAAGe,MAAM;UACnD1E,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE,CAAC,EACJ,IAAAK,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,aACF,CACF;IACF,CAAC;IACDO,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDkF,QAAQ,EAAE;MACRxH,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACD+B,cAAc,EAAE;MACdrE,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDgC,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACDsI,QAAQ,EAAE;MACR5K,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDyB,OAAO,EAAE;MACP/D,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDuI,QAAQ,EAAE;MACR7K,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEF1C,UAAU,CAAC,sBAAsB,EAAE;EACjCY,OAAO,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC;EACrEmB,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC;EACjDlB,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EAChCX,MAAM,EAAE;IACNqB,GAAG,EAAE;MACHnB,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDO,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACD+B,cAAc,EAAE;MACdrE,QAAQ,EAEJ,IAAAyB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDgC,UAAU,EAAE;MACVtE,QAAQ,EAAE,IAAAkC,kBAAW,EAAC,WAAW,CAAC;MAClCI,QAAQ,EAAE;IACZ,CAAC;IACD6G,MAAM,EAAE;MACNnJ,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpCR,OAAO,EAAE;IACX,CAAC;IACDyK,QAAQ,EAAE;MACR5K,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDA,QAAQ,EAAE;MACRtC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDkF,QAAQ,EAAE;MACRxH,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpC2B,QAAQ,EAAE;IACZ,CAAC;IACDuI,QAAQ,EAAE;MACR7K,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEF1C,UAAU,CAAC,oBAAoB,EAAE;EAC/B+B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;EACpDnB,OAAO,EAAE,CACP,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,MAAM,CACP;EACDC,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,SAAS,CACV;EACDX,MAAM,EAAAc,MAAA,CAAAC,MAAA,KACDwI,gCAAgC,CAAC,CAAC,EAClCzF,4BAA4B,CAAC,CAAC;IACjCuC,IAAI,EAAE;MACJnG,QAAQ,EAAE,IAAAe,kBAAW,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;MAC7CZ,OAAO,EAAE;IACX,CAAC;IACDgB,GAAG,EAAE;MACHnB,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDU,IAAI,EAAE;MACJnC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC;AAEL,CAAC,CAAC;AAEF7B,UAAU,CAAC,aAAa,EAAE;EACxBY,OAAO,EAAE,CAAC,IAAI,CAAC;EACfC,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBX,MAAM,EAAE;IACNkE,EAAE,EAAE;MACFhE,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,aAAa,EAAE;EACxBY,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBV,MAAM,EAAE;IACNqC,IAAI,EAAE,IAAAC,0BAAmB,EAAC,WAAW;EACvC,CAAC;EACD3B,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,gBAAgB;AACvD,CAAC,CAAC;AAGFb,UAAU,CAAC,iBAAiB,EAAE;EAC5BY,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;EACzBV,MAAM,EAAE;IACNqB,GAAG,EAAE;MACHnB,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACDO,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAyB,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC", "ignoreList": []}