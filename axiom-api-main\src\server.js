// Carrega as variáveis de ambiente do arquivo .env
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const { testConnection, initializeTables } = require('./config/database');
const messageRoutes = require('./routes/messageRoutes');
const authRoutes = require('./routes/authRoutes');
const instanceRoutes = require('./routes/instanceRoutes');

const app = express();

// Middlewares essenciais
app.use(cors()); // Permite requisições de outras origens
app.use(express.json()); // Permite que o servidor entenda JSON

// Rota principal de teste
app.get('/', (req, res) => {
    res.json({
        message: 'Axiom API v1.0 - Status: Running'
    });
});

// Rotas da aplicação
app.use('/api/v1', messageRoutes);
app.use('/api/v1', authRoutes);
app.use('/api/v1', instanceRoutes);

// Função para inicializar o servidor
const startServer = async () => {
    try {
        // Testar conexão com o banco
        await testConnection();

        // Inicializar tabelas
        await initializeTables();

        // Iniciar servidor
        const PORT = process.env.PORT || 3001;
        app.listen(PORT, () => {
            console.log(`[AXIOM API] Servidor rodando na porta ${PORT}`);
            console.log(`[AXIOM API] Ambiente: ${process.env.NODE_ENV || 'development'}`);
            console.log(`[AXIOM API] Banco de dados: ${process.env.DB_NAME || 'axiom_api'}`);
        });

    } catch (error) {
        console.error('[AXIOM API] Erro ao inicializar servidor:', error);
        process.exit(1);
    }
};

// Inicializar servidor
startServer();
