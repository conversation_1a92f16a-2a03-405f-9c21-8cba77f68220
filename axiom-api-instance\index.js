const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const express = require('express');

console.log('[AXIOM] Iniciando a Célula de Instância...');

// --- Variáveis de Estado Globais ---
let qrCodeData = null;
let connectionStatus = 'DISCONNECTED';

// --- Inicialização do Servidor Express (Executado apenas uma vez) ---
const app = express();
app.use(express.json());
const internalApiPort = 8080;

// Endpoint para obter o STATUS da conexão (Sempre disponível)
app.get('/status', (req, res) => {
    res.status(200).json({ status: connectionStatus });
});

// Endpoint para obter o QR CODE (Sempre disponível)
app.get('/qr', (req, res) => {
    if (qrCodeData) {
        res.status(200).json({ qr: qrCodeData });
    } else {
        res.status(404).json({ message: 'QR Code não disponível no momento.' });
    }
});

// Endpoint para enviar mensagem de TEXTO
app.post('/send-text', async (req, res) => {
    if (connectionStatus !== 'CONNECTED') {
        return res.status(503).json({ status: 'error', message: 'Cliente WhatsApp não está conectado.' });
    }
    const { phone, message } = req.body;
    if (!phone || !message) {
        return res.status(400).json({ status: 'error', message: 'Parâmetros "phone" e "message" são obrigatórios.' });
    }
    try {
        await client.sendMessage(`${phone}@c.us`, message);
        res.status(200).json({ status: 'success', message: 'Mensagem enviada.' });
    } catch (error) {
        console.error('[INTERNAL API] Erro ao enviar mensagem:', error);
        res.status(500).json({ status: 'error', message: 'Falha ao enviar mensagem via WhatsApp.' });
    }
});

// Endpoint para enviar IMAGEM a partir de uma URL
app.post('/send-image', async (req, res) => {
    if (connectionStatus !== 'CONNECTED') {
        return res.status(503).json({ status: 'error', message: 'Cliente WhatsApp não está conectado.' });
    }
    const { phone, url, caption } = req.body;
    if (!phone || !url) return res.status(400).json({ status: 'error', message: 'Parâmetros "phone" e "url" são obrigatórios.' });

    try {
        const media = await MessageMedia.fromUrl(url, { unsafeMime: true });
        await client.sendMessage(`${phone}@c.us`, media, { caption: caption || '' });
        res.status(200).json({ status: 'success', message: 'Imagem enviada.' });
    } catch (error) {
        console.error('[INTERNAL API] Erro ao enviar imagem:', error);
        res.status(500).json({ status: 'error', message: 'Falha ao processar e enviar imagem.' });
    }
});

// Endpoint para enviar ÁUDIO a partir de uma URL
app.post('/send-audio', async (req, res) => {
    if (connectionStatus !== 'CONNECTED') {
        return res.status(503).json({ status: 'error', message: 'Cliente WhatsApp não está conectado.' });
    }
    const { phone, url } = req.body;
    if (!phone || !url) return res.status(400).json({ status: 'error', message: 'Parâmetros "phone" e "url" são obrigatórios.' });

    try {
        console.log(`[INTERNAL API] Processando áudio de: ${url}`);
        const media = await MessageMedia.fromUrl(url, {
            unsafeMime: true,
            timeout: 30000
        });
        console.log(`[INTERNAL API] Mídia processada, tipo: ${media.mimetype}`);

        // Força o tipo como áudio se não foi detectado corretamente
        if (!media.mimetype || !media.mimetype.startsWith('audio/')) {
            media.mimetype = 'audio/mpeg';
        }

        await client.sendMessage(`${phone}@c.us`, media);
        res.status(200).json({ status: 'success', message: 'Áudio enviado.' });
    } catch (error) {
        console.error('[INTERNAL API] Erro ao enviar áudio:', error);
        res.status(500).json({ status: 'error', message: 'Falha ao processar e enviar áudio.' });
    }
});

// Endpoint para enviar VÍDEO a partir de uma URL
app.post('/send-video', async (req, res) => {
    if (connectionStatus !== 'CONNECTED') {
        return res.status(503).json({ status: 'error', message: 'Cliente WhatsApp não está conectado.' });
    }
    const { phone, url, caption } = req.body;
    if (!phone || !url) return res.status(400).json({ status: 'error', message: 'Parâmetros "phone" e "url" são obrigatórios.' });

    try {
        console.log(`[INTERNAL API] Processando vídeo de: ${url}`);

        const media = await MessageMedia.fromUrl(url, {
            unsafeMime: true,
            timeout: 30000
        });

        console.log(`[INTERNAL API] Mídia processada, tamanho: ${media.data.length}, tipo: ${media.mimetype}`);

        // Verifica se o arquivo não é muito grande (limite do WhatsApp)
        const maxSize = 16 * 1024 * 1024; // 16MB
        if (media.data.length > maxSize) {
            return res.status(400).json({
                status: 'error',
                message: 'Arquivo muito grande. Máximo 16MB para vídeos.'
            });
        }

        // ESTRATÉGIA SIMPLIFICADA: Sempre envia como documento para garantir funcionamento
        console.log('[INTERNAL API] Enviando vídeo como documento para garantir entrega...');
        const filename = caption ? `${caption}.mp4` : 'video.mp4';
        media.filename = filename;

        await client.sendMessage(`${phone}@c.us`, media);
        res.status(200).json({
            status: 'success',
            message: 'Vídeo enviado como documento.',
            method: 'document',
            note: 'Vídeos são enviados como documentos para garantir compatibilidade'
        });
    } catch (error) {
        console.error('[INTERNAL API] Erro detalhado ao enviar vídeo:', error.message);
        res.status(500).json({ status: 'error', message: `Falha ao processar vídeo: ${error.message}` });
    }
});

// Endpoint para enviar DOCUMENTO a partir de uma URL
app.post('/send-document', async (req, res) => {
    if (connectionStatus !== 'CONNECTED') {
        return res.status(503).json({ status: 'error', message: 'Cliente WhatsApp não está conectado.' });
    }
    const { phone, url, filename } = req.body;
    if (!phone || !url || !filename) return res.status(400).json({ status: 'error', message: 'Parâmetros "phone", "url" e "filename" são obrigatórios.' });

    try {
        const media = await MessageMedia.fromUrl(url, { unsafeMime: true });
        media.filename = filename; // Atribui o nome do arquivo
        await client.sendMessage(`${phone}@c.us`, media);
        res.status(200).json({ status: 'success', message: 'Documento enviado.' });
    } catch (error) {
        console.error('[INTERNAL API] Erro ao enviar documento:', error);
        res.status(500).json({ status: 'error', message: 'Falha ao processar e enviar documento.' });
    }
});



app.listen(internalApiPort, () => {
    console.log(`[AXIOM] API Interna rodando na porta ${internalApiPort}. Aguardando comandos.`);
});

// --- Configuração e Eventos do Cliente WhatsApp ---
const client = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--user-data-dir=/tmp/chromium-user-data-' + Date.now()
        ]
    }
});

client.on('qr', (qr) => {
    console.log('[AXIOM] QR Code Recebido. Exibindo no terminal...');
    qrcode.generate(qr, { small: true });
    qrCodeData = qr;
    connectionStatus = 'AWAITING_QR_SCAN';
});

client.on('ready', () => {
    console.log('[AXIOM] Cliente está pronto! Conexão estabelecida.');
    qrCodeData = null;
    connectionStatus = 'CONNECTED';
});

client.on('authenticated', () => {
    console.log('[AXIOM] Autenticado com sucesso.');
});

client.on('auth_failure', (msg) => {
    console.error('[AXIOM] Falha na Autenticação!', msg);
    connectionStatus = 'DISCONNECTED';
    process.exit(1);
});

client.on('disconnected', (reason) => {
    console.warn('[AXIOM] Cliente foi desconectado!', reason);
    connectionStatus = 'DISCONNECTED';
    qrCodeData = null;

    // Aguarda um pouco antes de tentar reinicializar
    setTimeout(() => {
        console.log('[AXIOM] Tentando reinicializar cliente...');
        try {
            client.initialize();
        } catch (error) {
            console.error('[AXIOM] Erro ao reinicializar cliente:', error);
        }
    }, 5000);
});

// Adiciona tratamento de erro geral
client.on('error', (error) => {
    console.error('[AXIOM] Erro no cliente WhatsApp:', error);
    connectionStatus = 'DISCONNECTED';
});

// Função para inicializar o cliente com retry
const initializeClient = async () => {
    try {
        console.log('[AXIOM] Inicializando cliente WhatsApp...');
        await client.initialize();
    } catch (error) {
        console.error('[AXIOM] Erro ao inicializar cliente:', error);
        connectionStatus = 'DISCONNECTED';

        // Tenta novamente após 10 segundos
        setTimeout(() => {
            console.log('[AXIOM] Tentando inicializar novamente...');
            initializeClient();
        }, 10000);
    }
};

// Inicia o cliente
initializeClient();
