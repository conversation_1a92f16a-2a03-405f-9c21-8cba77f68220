# 📱 Teste do Módulo 3: Mensagens de Mídia - Axiom API

## ✅ Status da Implementação

- ✅ **API Principal funcionando** na porta 3000
- ✅ **API Instance funcionando** na porta 8080 (interna)
- ✅ **QR Code gerado** - Aguardando escaneamento
- ✅ **Novos endpoints implementados** para cada tipo de mídia

## 🔧 Endpoints Disponíveis

### 1. **Enviar Imagem**
```bash
# PowerShell
$body = @{
    phone = "5511999999999"
    url = "https://picsum.photos/800/600"
    caption = "Imagem de teste enviada via Axiom API"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3000/api/v1/send-image" -Method POST -Body $body -ContentType "application/json"
```

### 2. **Enviar Áudio**
```bash
# PowerShell
$body = @{
    phone = "5511999999999"
    url = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3000/api/v1/send-audio" -Method POST -Body $body -ContentType "application/json"
```

### 3. **Enviar Vídeo**
```bash
# PowerShell
$body = @{
    phone = "5511999999999"
    url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    caption = "Vídeo de teste enviado via Axiom API"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3000/api/v1/send-video" -Method POST -Body $body -ContentType "application/json"
```

### 4. **Enviar Documento**
```bash
# PowerShell
$body = @{
    phone = "5511999999999"
    url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
    filename = "documento-teste.pdf"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3000/api/v1/send-document" -Method POST -Body $body -ContentType "application/json"
```

## 🧪 Testes de Validação

### Teste 1: Verificar Status da Conexão
```bash
Invoke-WebRequest -Uri "http://localhost:3000/api/v1/status" -Method GET
```
**Resultado Esperado:** `{"status":"AWAITING_QR_SCAN"}` ou `{"status":"CONNECTED"}`

### Teste 2: Obter QR Code
```bash
Invoke-WebRequest -Uri "http://localhost:3000/api/v1/qr" -Method GET
```
**Resultado Esperado:** Imagem PNG do QR Code

### Teste 3: Endpoint Principal
```bash
Invoke-WebRequest -Uri "http://localhost:3000/" -Method GET
```
**Resultado Esperado:** `{"message":"Axiom API v1.0 - Status: Running"}`

## 📋 Checklist de Testes

- [ ] **Escanear QR Code** com WhatsApp para conectar
- [ ] **Verificar status** = "CONNECTED" após escaneamento
- [ ] **Testar envio de imagem** com URL válida
- [ ] **Testar envio de áudio** com URL válida
- [ ] **Testar envio de vídeo** com URL válida
- [ ] **Testar envio de documento** com URL válida
- [ ] **Verificar logs** nos containers para erros
- [ ] **Testar validação** de parâmetros obrigatórios

## 🚀 Próximos Passos

1. **Conectar WhatsApp:** Escaneie o QR Code exibido no terminal
2. **Testar endpoints:** Use os comandos acima com números reais
3. **Validar funcionalidades:** Confirme que as mídias são enviadas corretamente
4. **Implementar testes automatizados:** Criar suite de testes unitários

## 🔍 Troubleshooting

### Problema: Container não inicia
- **Solução:** Execute `docker compose down` e depois `docker compose up --build`

### Problema: QR Code não aparece
- **Solução:** Verifique os logs com `docker compose logs api-instance-1`

### Problema: Erro 503 ao enviar mídia
- **Solução:** Certifique-se que o WhatsApp está conectado (status = "CONNECTED")

### Problema: Erro 400 parâmetros obrigatórios
- **Solução:** Verifique se todos os campos obrigatórios estão presentes no JSON

## 📊 Estrutura de Resposta

### Sucesso (200)
```json
{
    "status": "success",
    "message": "Imagem enviada."
}
```

### Erro de Validação (400)
```json
{
    "status": "error",
    "message": "Parâmetros \"phone\" e \"url\" são obrigatórios."
}
```

### Erro de Conexão (503)
```json
{
    "status": "error",
    "message": "Cliente WhatsApp não está conectado."
}
```
