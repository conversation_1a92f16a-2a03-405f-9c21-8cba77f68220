const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

const app = express();

// Middlewares essenciais
app.use(cors());
app.use(express.json());

// Banco em memória (com estrutura multi-tenant)
let users = [];
let instances = [];

// Configuração JWT
const JWT_SECRET = 'axiom-secret-key-super-secure-2024';

// Middleware de log
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Middleware de autenticação
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({
                error: 'Token não fornecido',
                message: 'É necessário fornecer um token de autenticação válido'
            });
        }

        let decoded;
        try {
            decoded = jwt.verify(token, JWT_SECRET);
        } catch (jwtError) {
            if (jwtError.name === 'TokenExpiredError') {
                return res.status(401).json({
                    error: 'Token expirado',
                    message: 'O token de autenticação expirou. Faça login novamente.'
                });
            } else {
                return res.status(401).json({
                    error: 'Token inválido',
                    message: 'O token fornecido não é válido'
                });
            }
        }

        const user = users.find(u => u.id === decoded.id && u.is_active !== false);
        
        if (!user) {
            return res.status(401).json({
                error: 'Usuário não encontrado',
                message: 'Token válido mas usuário não existe mais'
            });
        }

        req.user = {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role || 'user'
        };

        console.log(`[AUTH] Usuário autenticado: ${user.email} (${user.id})`);
        next();
    } catch (error) {
        console.error('[AUTH] Erro no middleware de autenticação:', error);
        return res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao processar autenticação'
        });
    }
};

// Middleware para verificar ownership de instâncias
const requireInstanceOwnership = async (req, res, next) => {
    try {
        const instanceId = req.params.id;
        
        if (!instanceId) {
            return res.status(400).json({
                error: 'ID da instância não fornecido',
                message: 'É necessário fornecer o ID da instância'
            });
        }

        // Administradores podem acessar qualquer instância
        if (req.user.role === 'admin') {
            return next();
        }

        // Verificar se a instância pertence ao usuário
        const instance = instances.find(i => i.id === instanceId);

        if (!instance) {
            return res.status(404).json({
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        if (instance.owner_id !== req.user.id) {
            return res.status(403).json({
                error: 'Acesso negado',
                message: 'Você não tem permissão para acessar esta instância'
            });
        }

        next();
    } catch (error) {
        console.error('[AUTH] Erro ao verificar ownership da instância:', error);
        return res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao verificar permissões da instância'
        });
    }
};

// Função para gerar token JWT
const generateToken = (user) => {
    const payload = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role || 'user'
    };

    return jwt.sign(payload, JWT_SECRET, {
        expiresIn: '24h',
        issuer: 'axiom-api',
        audience: 'axiom-users'
    });
};

// =====================================================
// ROTAS DE AUTENTICAÇÃO
// =====================================================

// Rota principal
app.get('/', (req, res) => {
    console.log('Rota / acessada');
    res.json({ 
        message: 'Axiom API v1.0 - Multi-tenant WhatsApp System',
        status: 'Running',
        features: ['JWT Authentication', 'Multi-tenant Architecture', 'Instance Management'],
        endpoints: {
            auth: '/api/v1/auth/*',
            instances: '/api/v1/instances/*',
            frontend: '/app',
            dashboard: '/dashboard'
        }
    });
});

// Registro de usuário
app.post('/api/v1/auth/register', async (req, res) => {
    try {
        console.log('Registro iniciado:', req.body);
        
        const { name, email, password, whatsappNumber } = req.body;
        
        if (!name || !email || !password) {
            console.log('Dados obrigatórios faltando');
            return res.status(400).json({ 
                error: 'Dados obrigatórios',
                message: 'Nome, email e senha são obrigatórios' 
            });
        }
        
        // Verificar se usuário já existe
        const existingUser = users.find(u => u.email === email);
        if (existingUser) {
            console.log('Usuário já existe:', email);
            return res.status(400).json({ 
                error: 'Usuário já existe',
                message: 'Já existe um usuário com este email' 
            });
        }
        
        // Hash da senha
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Criar usuário
        const user = {
            id: uuidv4(),
            name,
            email,
            password: hashedPassword,
            whatsappNumber: whatsappNumber || null,
            role: 'user',
            is_active: true,
            email_verified: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        users.push(user);
        console.log('Usuário criado:', { id: user.id, email: user.email });
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.status(201).json({
            success: true,
            message: 'Usuário criado com sucesso',
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no registro:', error);
        res.status(500).json({ 
            error: 'Erro interno do servidor',
            message: 'Erro ao criar usuário' 
        });
    }
});

// Login de usuário
app.post('/api/v1/auth/login', async (req, res) => {
    try {
        console.log('Login iniciado:', { email: req.body.email });
        
        const { email, password } = req.body;
        
        if (!email || !password) {
            console.log('Email ou senha faltando');
            return res.status(400).json({ 
                error: 'Dados obrigatórios',
                message: 'Email e senha são obrigatórios' 
            });
        }
        
        // Buscar usuário
        const user = users.find(u => u.email === email);
        if (!user) {
            console.log('Usuário não encontrado:', email);
            return res.status(401).json({ 
                error: 'Credenciais inválidas',
                message: 'Email ou senha incorretos' 
            });
        }
        
        // Verificar senha
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            console.log('Senha inválida para:', email);
            return res.status(401).json({ 
                error: 'Credenciais inválidas',
                message: 'Email ou senha incorretos' 
            });
        }
        
        // Gerar token
        const token = generateToken(user);
        
        console.log('Login realizado com sucesso:', email);
        
        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.json({
            success: true,
            message: 'Login realizado com sucesso',
            token,
            user: userResponse
        });
        
    } catch (error) {
        console.error('Erro no login:', error);
        res.status(500).json({ 
            error: 'Erro interno do servidor',
            message: 'Erro ao fazer login' 
        });
    }
});

// =====================================================
// ROTAS DE INSTÂNCIAS (PROTEGIDAS)
// =====================================================

// Listar instâncias do usuário
app.get('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log(`[INSTANCES] Listando instâncias para usuário: ${req.user.email}`);
        
        const userInstances = instances.filter(instance => instance.owner_id === req.user.id);
        
        // Calcular estatísticas
        const stats = {
            total: userInstances.length,
            connected: userInstances.filter(i => i.status === 'CONNECTED').length,
            created: userInstances.filter(i => i.status === 'CREATED').length,
            disconnected: userInstances.filter(i => i.status === 'DISCONNECTED').length,
            error: userInstances.filter(i => i.status === 'ERROR').length
        };
        
        console.log(`[INSTANCES] ${userInstances.length} instâncias encontradas`);
        
        res.json({
            success: true,
            message: 'Instâncias listadas com sucesso',
            instances: userInstances,
            stats,
            total: userInstances.length
        });
    } catch (error) {
        console.error('[INSTANCES] Erro ao listar instâncias:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar instâncias'
        });
    }
});

// Criar nova instância
app.post('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        const { name, webhook_url } = req.body;

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                error: 'Nome obrigatório',
                message: 'O nome da instância é obrigatório'
            });
        }

        // Gerar ID único para a instância
        const instanceId = `axiom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const id = uuidv4();

        const instanceData = {
            id,
            name: name.trim(),
            owner_id: req.user.id,
            instance_id: instanceId,
            status: 'CREATED',
            webhook_url: webhook_url || null,
            api_key: null,
            qr_code: null,
            session_data: null,
            settings: {},
            last_activity: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        instances.push(instanceData);

        console.log(`[INSTANCES] Instância ${instanceId} criada para usuário: ${req.user.email}`);

        res.status(201).json({
            success: true,
            message: 'Instância criada com sucesso',
            instance: {
                id: instanceData.id,
                name: instanceData.name,
                instance_id: instanceData.instance_id,
                status: instanceData.status,
                webhook_url: instanceData.webhook_url,
                created_at: instanceData.created_at
            }
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao criar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao criar instância'
        });
    }
});

// Obter instância específica
app.get('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        res.json({
            success: true,
            instance
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar instância'
        });
    }
});

// Atualizar instância
app.put('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const { name, webhook_url } = req.body;

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                error: 'Nome obrigatório',
                message: 'O nome da instância é obrigatório'
            });
        }

        const instanceIndex = instances.findIndex(i => i.id === id);
        
        instances[instanceIndex] = {
            ...instances[instanceIndex],
            name: name.trim(),
            webhook_url: webhook_url || null,
            updated_at: new Date().toISOString()
        };

        res.json({
            success: true,
            message: 'Instância atualizada com sucesso',
            instance: instances[instanceIndex]
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao atualizar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao atualizar instância'
        });
    }
});

// Deletar instância
app.delete('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        
        const instanceIndex = instances.findIndex(i => i.id === id);
        const deletedInstance = instances.splice(instanceIndex, 1)[0];
        
        console.log(`[INSTANCES] Instância ${deletedInstance.instance_id} deletada`);

        res.json({
            success: true,
            message: 'Instância deletada com sucesso'
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao deletar instância:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao deletar instância'
        });
    }
});

// Status da instância
app.get('/api/v1/instances/:id/status', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        res.json({
            success: true,
            status: instance.status,
            last_activity: instance.last_activity,
            instance_id: instance.instance_id
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar status:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar status da instância'
        });
    }
});

// QR Code da instância
app.get('/api/v1/instances/:id/qr', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        // Simular QR code
        const qrCode = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;

        res.json({
            success: true,
            qr_code: qrCode,
            status: instance.status,
            message: instance.status === 'CREATED' ? 'Escaneie o QR Code para conectar' : 'Instância já conectada'
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar QR:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar QR Code'
        });
    }
});

// =====================================================
// FRONTEND INTEGRADO
// =====================================================

// Página de login
app.get('/app', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Login</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <div class="min-h-screen flex items-center justify-center px-4">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <span class="text-white font-bold text-2xl">A</span>
                        </div>
                        <h2 class="text-3xl font-bold text-white">Axiom API</h2>
                        <p class="mt-2 text-gray-400">Sistema Multi-tenant WhatsApp</p>
                    </div>

                    <div id="loginForm">
                        <form onsubmit="handleLogin(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="loginEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>" value="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="loginPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Sua senha" value="12345678">
                                </div>
                            </div>

                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Entrar
                            </button>

                            <button type="button" onclick="showRegister()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Criar Conta
                            </button>
                        </form>
                    </div>

                    <div id="registerForm" class="hidden">
                        <form onsubmit="handleRegister(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome</label>
                                    <input id="registerName" type="text" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Seu nome">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="registerEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="registerPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Mínimo 8 caracteres">
                                </div>
                            </div>

                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                Criar Conta
                            </button>

                            <button type="button" onclick="showLogin()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Voltar ao Login
                            </button>
                        </form>
                    </div>

                    <div id="result" class="mt-4 p-4 rounded-lg hidden">
                        <p id="resultText" class="text-white"></p>
                    </div>
                </div>
            </div>

            <script>
                function showRegister() {
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('registerForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }

                function showLogin() {
                    document.getElementById('registerForm').classList.add('hidden');
                    document.getElementById('loginForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }

                async function handleRegister(event) {
                    event.preventDefault();

                    const name = document.getElementById('registerName').value;
                    const email = document.getElementById('registerEmail').value;
                    const password = document.getElementById('registerPassword').value;

                    try {
                        const response = await fetch('/api/v1/auth/register', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ name, email, password })
                        });

                        const data = await response.json();
                        showResult(data.message, response.ok);

                        if (response.ok) {
                            setTimeout(() => showLogin(), 2000);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }

                async function handleLogin(event) {
                    event.preventDefault();

                    const email = document.getElementById('loginEmail').value;
                    const password = document.getElementById('loginPassword').value;

                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ email, password })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            showResult('Login realizado com sucesso! Redirecionando...', true);
                            localStorage.setItem('axiom-token', data.token);
                            localStorage.setItem('axiom-user', JSON.stringify(data.user));
                            setTimeout(() => {
                                window.location.href = '/dashboard';
                            }, 1500);
                        } else {
                            showResult(data.message || 'Erro no login', false);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }

                function showResult(message, isSuccess) {
                    const resultDiv = document.getElementById('result');
                    const resultText = document.getElementById('resultText');

                    resultText.textContent = message;
                    resultDiv.classList.remove('hidden', 'bg-green-800', 'bg-red-800');
                    resultDiv.classList.add(isSuccess ? 'bg-green-800' : 'bg-red-800');
                }
            </script>
        </body>
        </html>
    `);
});

// Inicializar servidor
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`🚀 Axiom API Multi-tenant rodando na porta ${PORT}`);
    console.log(`📱 Frontend: http://localhost:${PORT}/app`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
    console.log(`🔗 API: http://localhost:${PORT}/api/v1`);
    console.log(`🏢 Arquitetura: Multi-tenant com isolamento de dados`);
    console.log(`🔐 Autenticação: JWT com middleware robusto`);
    console.log(`📊 Usuários: ${users.length}`);
    console.log(`📱 Instâncias: ${instances.length}`);
});

module.exports = app;
