const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

// Importar middlewares e rotas organizadas
const { authenticateToken, requireInstanceOwnership, generateToken } = require('./src/middleware/authMiddleware');
const authRoutes = require('./src/routes/authRoutes');
const instanceRoutes = require('./src/routes/instanceRoutes');
const messageRoutes = require('./src/routes/messageRoutes');

const app = express();

// Middlewares essenciais
app.use(cors());
app.use(express.json());

// Banco em memória (com estrutura multi-tenant)
let users = [];
let instances = [];

// Configuração JWT
const JWT_SECRET = 'axiom-secret-key-super-secure-2024';

// Middleware de log
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Configuração para compatibilidade com banco em memória

// =====================================================
// ROTAS ORGANIZADAS
// =====================================================

// Rota principal
app.get('/', (req, res) => {
    console.log('Rota / acessada');
    res.json({
        message: 'Axiom API v1.0 - Multi-tenant WhatsApp System',
        status: 'Running',
        features: ['JWT Authentication', 'Multi-tenant Architecture', 'Instance Management'],
        endpoints: {
            auth: '/api/v1/auth/*',
            instances: '/api/v1/instances/*',
            frontend: '/app',
            dashboard: '/dashboard'
        }
    });
});

// =====================================================
// ROTAS DE AUTENTICAÇÃO (BANCO EM MEMÓRIA)
// =====================================================

// Registro de usuário
app.post('/api/v1/auth/register', async (req, res) => {
    try {
        console.log('[AUTH] Registro iniciado:', req.body);

        const { name, email, password, whatsappNumber } = req.body;

        if (!name || !email || !password) {
            console.log('[AUTH] Dados obrigatórios faltando');
            return res.status(400).json({
                success: false,
                error: 'Dados obrigatórios',
                message: 'Nome, email e senha são obrigatórios'
            });
        }

        // Verificar se usuário já existe
        const existingUser = users.find(u => u.email === email);
        if (existingUser) {
            console.log('[AUTH] Usuário já existe:', email);
            return res.status(400).json({
                success: false,
                error: 'Usuário já existe',
                message: 'Já existe um usuário com este email'
            });
        }

        // Hash da senha
        const hashedPassword = await bcrypt.hash(password, 10);

        // Criar usuário
        const user = {
            id: uuidv4(),
            name,
            email,
            password: hashedPassword,
            whatsapp_number: whatsappNumber || null,
            role: 'user',
            is_active: true,
            email_verified: false,
            api_token: `axiom_${uuidv4().replace(/-/g, '')}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        users.push(user);
        console.log('[AUTH] Usuário criado:', { id: user.id, email: user.email });

        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.status(201).json({
            success: true,
            message: 'Usuário criado com sucesso',
            user: userResponse
        });

    } catch (error) {
        console.error('[AUTH] Erro no registro:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao criar usuário'
        });
    }
});

// Login de usuário
app.post('/api/v1/auth/login', async (req, res) => {
    try {
        console.log('[AUTH] Login iniciado:', { email: req.body.email });

        const { email, password } = req.body;

        if (!email || !password) {
            console.log('[AUTH] Email ou senha faltando');
            return res.status(400).json({
                success: false,
                error: 'Dados obrigatórios',
                message: 'Email e senha são obrigatórios'
            });
        }

        // Buscar usuário
        const user = users.find(u => u.email === email);
        if (!user) {
            console.log('[AUTH] Usuário não encontrado:', email);
            return res.status(401).json({
                success: false,
                error: 'Credenciais inválidas',
                message: 'Email ou senha incorretos'
            });
        }

        // Verificar senha
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            console.log('[AUTH] Senha inválida para:', email);
            return res.status(401).json({
                success: false,
                error: 'Credenciais inválidas',
                message: 'Email ou senha incorretos'
            });
        }

        // Gerar token
        const token = generateToken(user);

        console.log('[AUTH] Login realizado com sucesso:', email);

        // Retornar sem a senha
        const { password: _, ...userResponse } = user;
        res.json({
            success: true,
            message: 'Login realizado com sucesso',
            token,
            user: userResponse
        });

    } catch (error) {
        console.error('[AUTH] Erro no login:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao fazer login'
        });
    }
});

// Buscar token de API do usuário
app.get('/api/v1/auth/api-token', authenticateToken, (req, res) => {
    try {
        const user = users.find(u => u.id === req.user.id);

        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'Usuário não encontrado',
                message: 'Usuário não existe'
            });
        }

        res.json({
            success: true,
            message: 'Token de API recuperado com sucesso',
            api_token: user.api_token
        });

    } catch (error) {
        console.error('[AUTH] Erro ao buscar token de API:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar token de API'
        });
    }
});

// =====================================================
// ROTAS DE INSTÂNCIAS (BANCO EM MEMÓRIA)
// =====================================================

// Listar instâncias do usuário
app.get('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        console.log(`[INSTANCES] Listando instâncias para usuário: ${req.user.email}`);

        const userInstances = instances.filter(instance => instance.owner_id === req.user.id);

        // Calcular estatísticas
        const stats = {
            total: userInstances.length,
            connected: userInstances.filter(i => i.status === 'CONNECTED').length,
            created: userInstances.filter(i => i.status === 'CREATED').length,
            connecting: userInstances.filter(i => i.status === 'CONNECTING').length,
            disconnected: userInstances.filter(i => i.status === 'DISCONNECTED').length,
            error: userInstances.filter(i => i.status === 'ERROR').length
        };

        console.log(`[INSTANCES] ${userInstances.length} instâncias encontradas`);

        res.json({
            success: true,
            message: 'Instâncias listadas com sucesso',
            instances: userInstances,
            stats,
            total: userInstances.length
        });
    } catch (error) {
        console.error('[INSTANCES] Erro ao listar instâncias:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar instâncias'
        });
    }
});

// Criar nova instância
app.post('/api/v1/instances', authenticateToken, (req, res) => {
    try {
        const { name, webhook_url } = req.body;

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Nome obrigatório',
                message: 'O nome da instância é obrigatório'
            });
        }

        // Verificar se já existe instância com esse nome para o usuário
        const existingInstance = instances.find(i =>
            i.owner_id === req.user.id &&
            i.name.toLowerCase() === name.trim().toLowerCase() &&
            i.status !== 'DELETED'
        );

        if (existingInstance) {
            return res.status(409).json({
                success: false,
                error: 'Nome já existe',
                message: 'Você já possui uma instância com este nome'
            });
        }

        // Gerar ID único para a instância
        const instanceId = `axiom_${uuidv4().replace(/-/g, '').substring(0, 16)}`;
        const id = uuidv4();

        const instanceData = {
            id,
            name: name.trim(),
            owner_id: req.user.id,
            instance_id: instanceId,
            status: 'CREATED',
            webhook_url: webhook_url || null,
            qr_code: null,
            session_data: null,
            settings: {},
            last_activity: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        instances.push(instanceData);

        console.log(`[INSTANCES] Instância ${instanceId} criada para usuário: ${req.user.email}`);

        res.status(201).json({
            success: true,
            message: 'Instância criada com sucesso',
            instance: {
                id: instanceData.id,
                name: instanceData.name,
                instance_id: instanceData.instance_id,
                status: instanceData.status,
                webhook_url: instanceData.webhook_url,
                created_at: instanceData.created_at
            }
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao criar instância:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao criar instância'
        });
    }
});

// Obter instância específica
app.get('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        res.json({
            success: true,
            instance
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar instância:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar instância'
        });
    }
});

// Atualizar instância
app.put('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const { name, webhook_url } = req.body;

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Nome obrigatório',
                message: 'O nome da instância é obrigatório'
            });
        }

        const instanceIndex = instances.findIndex(i => i.id === id);

        if (instanceIndex === -1) {
            return res.status(404).json({
                success: false,
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        instances[instanceIndex] = {
            ...instances[instanceIndex],
            name: name.trim(),
            webhook_url: webhook_url || null,
            updated_at: new Date().toISOString()
        };

        res.json({
            success: true,
            message: 'Instância atualizada com sucesso',
            instance: instances[instanceIndex]
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao atualizar instância:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao atualizar instância'
        });
    }
});

// Deletar instância
app.delete('/api/v1/instances/:id', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;

        const instanceIndex = instances.findIndex(i => i.id === id);

        if (instanceIndex === -1) {
            return res.status(404).json({
                success: false,
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        const deletedInstance = instances.splice(instanceIndex, 1)[0];

        console.log(`[INSTANCES] Instância ${deletedInstance.instance_id} deletada`);

        res.json({
            success: true,
            message: 'Instância deletada com sucesso'
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao deletar instância:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao deletar instância'
        });
    }
});

// Status da instância
app.get('/api/v1/instances/:id/status', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        if (!instance) {
            return res.status(404).json({
                success: false,
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        res.json({
            success: true,
            status: instance.status,
            last_activity: instance.last_activity,
            instance_id: instance.instance_id
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar status:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar status da instância'
        });
    }
});

// QR Code da instância
app.get('/api/v1/instances/:id/qr', authenticateToken, requireInstanceOwnership, (req, res) => {
    try {
        const { id } = req.params;
        const instance = instances.find(i => i.id === id);

        if (!instance) {
            return res.status(404).json({
                success: false,
                error: 'Instância não encontrada',
                message: 'A instância solicitada não existe'
            });
        }

        // Simular QR code
        const qrCode = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;

        res.json({
            success: true,
            qr_code: qrCode,
            status: instance.status,
            message: instance.status === 'CREATED' ? 'Escaneie o QR Code para conectar' : 'Instância já conectada'
        });

    } catch (error) {
        console.error('[INSTANCES] Erro ao buscar QR:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: 'Erro ao buscar QR Code'
        });
    }
});

// =====================================================
// FRONTEND INTEGRADO
// =====================================================

// Página de login
app.get('/app', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Login</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <div class="min-h-screen flex items-center justify-center px-4">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <span class="text-white font-bold text-2xl">A</span>
                        </div>
                        <h2 class="text-3xl font-bold text-white">Axiom API</h2>
                        <p class="mt-2 text-gray-400">Sistema Multi-tenant WhatsApp</p>
                    </div>

                    <div id="loginForm">
                        <form onsubmit="handleLogin(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="loginEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>" value="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="loginPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Sua senha" value="12345678">
                                </div>
                            </div>

                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Entrar
                            </button>

                            <button type="button" onclick="showRegister()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Criar Conta
                            </button>
                        </form>
                    </div>

                    <div id="registerForm" class="hidden">
                        <form onsubmit="handleRegister(event)" class="mt-8 space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome</label>
                                    <input id="registerName" type="text" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Seu nome">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <input id="registerEmail" type="email" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Senha</label>
                                    <input id="registerPassword" type="password" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Mínimo 8 caracteres">
                                </div>
                            </div>

                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                Criar Conta
                            </button>

                            <button type="button" onclick="showLogin()" class="w-full flex justify-center py-2 px-4 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                                Voltar ao Login
                            </button>
                        </form>
                    </div>

                    <div id="result" class="mt-4 p-4 rounded-lg hidden">
                        <p id="resultText" class="text-white"></p>
                    </div>
                </div>
            </div>

            <script>
                function showRegister() {
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('registerForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }

                function showLogin() {
                    document.getElementById('registerForm').classList.add('hidden');
                    document.getElementById('loginForm').classList.remove('hidden');
                    document.getElementById('result').classList.add('hidden');
                }

                async function handleRegister(event) {
                    event.preventDefault();

                    const name = document.getElementById('registerName').value;
                    const email = document.getElementById('registerEmail').value;
                    const password = document.getElementById('registerPassword').value;

                    try {
                        const response = await fetch('/api/v1/auth/register', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ name, email, password })
                        });

                        const data = await response.json();
                        showResult(data.message, response.ok);

                        if (response.ok) {
                            setTimeout(() => showLogin(), 2000);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }

                async function handleLogin(event) {
                    event.preventDefault();

                    const email = document.getElementById('loginEmail').value;
                    const password = document.getElementById('loginPassword').value;

                    try {
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ email, password })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            showResult('Login realizado com sucesso! Redirecionando...', true);
                            localStorage.setItem('axiom-token', data.token);
                            localStorage.setItem('axiom-user', JSON.stringify(data.user));
                            setTimeout(() => {
                                window.location.href = '/dashboard';
                            }, 1500);
                        } else {
                            showResult(data.message || 'Erro no login', false);
                        }
                    } catch (error) {
                        showResult('Erro de conexão: ' + error.message, false);
                    }
                }

                function showResult(message, isSuccess) {
                    const resultDiv = document.getElementById('result');
                    const resultText = document.getElementById('resultText');

                    resultText.textContent = message;
                    resultDiv.classList.remove('hidden', 'bg-green-800', 'bg-red-800');
                    resultDiv.classList.add(isSuccess ? 'bg-green-800' : 'bg-red-800');
                }
            </script>
        </body>
        </html>
    `);
});

// Dashboard completo
app.get('/dashboard', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Axiom API - Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.tailwindcss.com"></script>
            <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
            <style>body { font-family: 'Inter', system-ui, sans-serif; }</style>
        </head>
        <body class="bg-gray-900 text-white">
            <!-- Sidebar -->
            <div class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-800">
                <div class="flex items-center justify-between h-16 px-4 border-b border-gray-700">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">A</span>
                        </div>
                        <span class="text-xl font-bold text-white">Axiom API</span>
                    </div>
                </div>

                <nav class="mt-8 px-4">
                    <div class="space-y-2">
                        <a href="#" onclick="showPage('dashboard')" class="nav-link bg-blue-600 text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                            Dashboard
                        </a>
                        <a href="#" onclick="showPage('instances')" class="nav-link text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="smartphone" class="mr-3 h-5 w-5"></i>
                            Instâncias Web
                        </a>
                        <a href="#" onclick="showPage('account')" class="nav-link text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i data-lucide="user" class="mr-3 h-5 w-5"></i>
                            Dados da Conta
                        </a>
                    </div>
                </nav>

                <!-- User info at bottom -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium" id="userInitial">U</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white truncate" id="userName">Usuário</p>
                            <p class="text-xs text-gray-400 truncate" id="userEmail"><EMAIL></p>
                        </div>
                    </div>
                    <button onclick="logout()" class="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:bg-red-600 hover:text-white rounded-lg transition-colors">
                        <i data-lucide="log-out" class="mr-3 h-4 w-4"></i>
                        Sair
                    </button>
                </div>
            </div>

            <!-- Main content -->
            <div class="pl-64">
                <main class="bg-gray-900 min-h-screen">
                    <!-- Dashboard Page -->
                    <div id="dashboardPage" class="p-6">
                        <div class="mb-8">
                            <h1 class="text-3xl font-bold text-white mb-2">Dashboard</h1>
                            <p class="text-gray-400">Bem-vindo de volta! Aqui está um resumo da sua conta.</p>
                        </div>

                        <!-- Metrics Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-blue-500/20 rounded-lg">
                                        <i data-lucide="smartphone" class="w-6 h-6 text-blue-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total de Instâncias</p>
                                        <p class="text-2xl font-bold text-white" id="totalInstances">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-green-500/20 rounded-lg">
                                        <i data-lucide="users" class="w-6 h-6 text-green-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Instâncias Conectadas</p>
                                        <p class="text-2xl font-bold text-white" id="connectedInstances">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-purple-500/20 rounded-lg">
                                        <i data-lucide="send" class="w-6 h-6 text-purple-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total Enviado</p>
                                        <p class="text-2xl font-bold text-white" id="totalSent">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:bg-gray-750 transition-colors">
                                <div class="flex items-center">
                                    <div class="p-3 bg-yellow-500/20 rounded-lg">
                                        <i data-lucide="message-square" class="w-6 h-6 text-yellow-400"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-400">Total Recebido</p>
                                        <p class="text-2xl font-bold text-white" id="totalReceived">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 mb-8">
                            <h2 class="text-xl font-bold text-white mb-6">Ações Rápidas</h2>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <button onclick="showPage('instances')" class="flex items-center justify-center p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="plus" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Nova Instância
                                </button>
                                <button onclick="showPage('instances')" class="flex items-center justify-center p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="smartphone" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Gerenciar Instâncias
                                </button>
                                <button onclick="alert('Relatórios em desenvolvimento!')" class="flex items-center justify-center p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors group">
                                    <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Ver Relatórios
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6">
                            <h2 class="text-xl font-bold text-white mb-6">Atividade Recente</h2>
                            <div id="recentActivity" class="space-y-4">
                                <div class="text-center py-4">
                                    <p class="text-gray-400 text-sm">Carregando atividades...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instances Page -->
                    <div id="instancesPage" class="p-6 hidden">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                            <div>
                                <h1 class="text-3xl font-bold text-white mb-2">Instâncias Web</h1>
                                <p class="text-gray-400">Gerencie suas instâncias de WhatsApp Web</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex space-x-3">
                                <button onclick="loadInstances()" class="flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                    Atualizar
                                </button>
                                <button onclick="showCreateInstanceModal()" class="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Adicionar Instância
                                </button>
                            </div>
                        </div>

                        <!-- Instances Table -->
                        <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden">
                            <div id="instancesTable">
                                <div class="p-8 text-center">
                                    <i data-lucide="smartphone" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                    <h3 class="text-lg font-medium text-white mb-2">Carregando instâncias...</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Page -->
                    <div id="accountPage" class="p-6 hidden">
                        <div class="mb-8">
                            <h1 class="text-3xl font-bold text-white mb-2">Dados da Conta</h1>
                            <p class="text-gray-400">Gerencie suas informações pessoais e preferências</p>
                        </div>

                        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6">
                            <h2 class="text-xl font-bold text-white mb-6">Informações Pessoais</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Nome Completo</label>
                                    <p class="text-white" id="accountName">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">E-mail</label>
                                    <p class="text-white" id="accountEmail">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">ID do Usuário</label>
                                    <p class="text-white font-mono text-sm" id="accountId">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Tipo de Conta</label>
                                    <p class="text-white capitalize" id="accountRole">user</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>

            <!-- Create Instance Modal -->
            <div id="createInstanceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 hidden">
                <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-md">
                    <h2 class="text-xl font-bold text-white mb-4">Nova Instância</h2>
                    <form onsubmit="createInstance(event)">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Nome da Instância</label>
                            <input type="text" id="instanceName" required placeholder="Ex: WhatsApp Vendas" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="hideCreateInstanceModal()" class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors">
                                Cancelar
                            </button>
                            <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                Criar Instância
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <script>
                // Global state
                let currentUser = null;
                let token = localStorage.getItem('axiom-token');
                let instances = [];

                // Initialize
                document.addEventListener('DOMContentLoaded', function() {
                    lucide.createIcons();
                    checkAuth();
                });

                function checkAuth() {
                    const savedToken = localStorage.getItem('axiom-token');
                    const savedUser = localStorage.getItem('axiom-user');

                    if (!savedToken || !savedUser) {
                        window.location.href = '/app';
                        return;
                    }

                    try {
                        token = savedToken;
                        currentUser = JSON.parse(savedUser);
                        updateUserInfo();
                        loadDashboardData();
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                        window.location.href = '/app';
                    }
                }

                function updateUserInfo() {
                    if (currentUser) {
                        document.getElementById('userInitial').textContent = currentUser.name.charAt(0).toUpperCase();
                        document.getElementById('userName').textContent = currentUser.name;
                        document.getElementById('userEmail').textContent = currentUser.email;

                        // Update account page
                        document.getElementById('accountName').textContent = currentUser.name;
                        document.getElementById('accountEmail').textContent = currentUser.email;
                        document.getElementById('accountId').textContent = currentUser.id;
                        document.getElementById('accountRole').textContent = currentUser.role || 'user';
                    }
                }

                async function loadDashboardData() {
                    try {
                        const response = await fetch('/api/v1/instances', {
                            headers: { 'Authorization': \`Bearer \${token}\` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            instances = data.instances || [];

                            const connectedCount = instances.filter(i => i.status === 'CONNECTED').length;

                            document.getElementById('totalInstances').textContent = instances.length;
                            document.getElementById('connectedInstances').textContent = connectedCount;
                            document.getElementById('totalSent').textContent = Math.floor(Math.random() * 5000 + 1000).toLocaleString();
                            document.getElementById('totalReceived').textContent = Math.floor(Math.random() * 3000 + 500).toLocaleString();

                            updateRecentActivity();
                        }
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                }

                function updateRecentActivity() {
                    const activityDiv = document.getElementById('recentActivity');

                    if (instances.length === 0) {
                        activityDiv.innerHTML = \`
                            <div class="text-center py-4">
                                <p class="text-gray-400 text-sm">Nenhuma instância criada ainda</p>
                                <button onclick="showPage('instances')" class="mt-2 text-blue-400 hover:text-blue-300 text-sm font-medium">
                                    Criar primeira instância
                                </button>
                            </div>
                        \`;
                    } else {
                        activityDiv.innerHTML = instances.slice(0, 3).map(instance => \`
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 rounded-full \${getStatusColor(instance.status)}"></div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-white truncate">\${instance.name}</p>
                                    <p class="text-xs text-gray-400">\${getStatusText(instance.status)}</p>
                                </div>
                            </div>
                        \`).join('');
                    }
                }

                function showPage(page) {
                    // Hide all pages
                    document.getElementById('dashboardPage').classList.add('hidden');
                    document.getElementById('instancesPage').classList.add('hidden');
                    document.getElementById('accountPage').classList.add('hidden');

                    // Show selected page
                    document.getElementById(page + 'Page').classList.remove('hidden');

                    // Update navigation
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('bg-blue-600', 'text-white');
                        link.classList.add('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
                    });

                    const activeLink = document.querySelector(\`[onclick="showPage('\${page}')"]\`);
                    if (activeLink) {
                        activeLink.classList.add('bg-blue-600', 'text-white');
                        activeLink.classList.remove('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
                    }

                    if (page === 'instances') {
                        loadInstances();
                    }
                }

                async function loadInstances() {
                    try {
                        const response = await fetch('/api/v1/instances', {
                            headers: { 'Authorization': \`Bearer \${token}\` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            instances = data.instances || [];
                            renderInstancesTable();
                        }
                    } catch (error) {
                        console.error('Error loading instances:', error);
                    }
                }

                function renderInstancesTable() {
                    const tableDiv = document.getElementById('instancesTable');

                    if (instances.length === 0) {
                        tableDiv.innerHTML = \`
                            <div class="p-8 text-center">
                                <i data-lucide="smartphone" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-lg font-medium text-white mb-2">Nenhuma instância criada</h3>
                                <p class="text-gray-400 mb-4">Crie sua primeira instância para começar a usar o WhatsApp Web</p>
                                <button onclick="showCreateInstanceModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Criar Primeira Instância
                                </button>
                            </div>
                        \`;
                    } else {
                        tableDiv.innerHTML = \`
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nome</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID da Instância</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Criado em</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-700">
                                        \${instances.map(instance => \`
                                            <tr class="hover:bg-gray-750 transition-colors">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-white">\${instance.name}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-300 font-mono">\${instance.instance_id}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="w-2 h-2 rounded-full mr-2 \${getStatusColor(instance.status)}"></div>
                                                        <span class="text-sm text-gray-300">\${getStatusText(instance.status)}</span>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                                    \${formatDate(instance.created_at)}
                                                </td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </div>
                        \`;
                    }

                    lucide.createIcons();
                }

                async function createInstance(event) {
                    event.preventDefault();

                    const name = document.getElementById('instanceName').value;

                    try {
                        const response = await fetch('/api/v1/instances', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': \`Bearer \${token}\`
                            },
                            body: JSON.stringify({ name })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            hideCreateInstanceModal();
                            loadInstances();
                            loadDashboardData();
                            alert('Instância criada com sucesso!');
                        } else {
                            alert(data.message || 'Erro ao criar instância');
                        }
                    } catch (error) {
                        console.error('Error creating instance:', error);
                        alert('Erro ao criar instância');
                    }
                }

                function showCreateInstanceModal() {
                    document.getElementById('createInstanceModal').classList.remove('hidden');
                    document.getElementById('instanceName').value = '';
                }

                function hideCreateInstanceModal() {
                    document.getElementById('createInstanceModal').classList.add('hidden');
                }

                function logout() {
                    localStorage.removeItem('axiom-token');
                    localStorage.removeItem('axiom-user');
                    window.location.href = '/app';
                }

                function getStatusColor(status) {
                    switch (status) {
                        case 'CONNECTED': return 'bg-green-500';
                        case 'CREATED': return 'bg-yellow-500';
                        case 'DISCONNECTED': return 'bg-red-500';
                        default: return 'bg-gray-500';
                    }
                }

                function getStatusText(status) {
                    switch (status) {
                        case 'CONNECTED': return 'Conectada';
                        case 'CREATED': return 'Aguardando QR';
                        case 'DISCONNECTED': return 'Desconectada';
                        default: return 'Desconhecido';
                    }
                }

                function formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            </script>
        </body>
        </html>
    `);
});

// Inicializar servidor
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`🚀 Axiom API Multi-tenant rodando na porta ${PORT}`);
    console.log(`📱 Frontend: http://localhost:${PORT}/app`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
    console.log(`🔗 API: http://localhost:${PORT}/api/v1`);
    console.log(`🏢 Arquitetura: Multi-tenant com isolamento de dados`);
    console.log(`🔐 Autenticação: JWT com middleware robusto`);
    console.log(`📊 Usuários: ${users.length}`);
    console.log(`📱 Instâncias: ${instances.length}`);
});

module.exports = app;
