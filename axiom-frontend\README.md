# Axiom API Frontend

Frontend moderno da plataforma Axiom API construído com React, TypeScript e Tailwind CSS.

## 🚀 Tecnologias Utilizadas

- **React 18** - Biblioteca para construção de interfaces
- **TypeScript** - Superset do JavaScript com tipagem estática
- **Tailwind CSS** - Framework CSS utilitário
- **React Router DOM** - Roteamento para aplicações React
- **Vite** - Build tool moderna e rápida

## 📁 Estrutura do Projeto

```
src/
├── components/     # Componentes reutilizáveis
├── pages/         # Páginas da aplicação
│   ├── HomePage.tsx
│   ├── LoginPage.tsx
│   └── RegisterPage.tsx
├── services/      # Serviços e APIs
├── App.tsx        # Componente principal
├── main.tsx       # Ponto de entrada
└── index.css      # Estilos globais
```

## 🎨 Páginas Implementadas

### 1. HomePage (`/`)
- Página de aterrissagem com design moderno
- Call-to-actions para registro e login
- Seção de features destacando os benefícios
- Design responsivo e atrativo

### 2. LoginPage (`/login`)
- Formulário de login com validação
- Campo de e-mail e senha
- Opção de mostrar/ocultar senha
- Link para recuperação de senha
- Integração com Google (UI pronta)

### 3. RegisterPage (`/register`)
- Formulário de cadastro completo
- Campos: nome, e-mail, WhatsApp, senha e confirmação
- Validação em tempo real das senhas
- Checkbox para termos de uso
- Integração com Google (UI pronta)

## 🎯 Características

- **Design Responsivo**: Funciona perfeitamente em desktop e mobile
- **Acessibilidade**: Seguindo boas práticas de acessibilidade
- **Validação Visual**: Feedback imediato para o usuário
- **Tipagem TypeScript**: Código mais seguro e manutenível
- **Performance**: Otimizado com Vite para carregamento rápido

## 🚀 Como Executar

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Executar em modo desenvolvimento:**
   ```bash
   npm run dev
   ```

3. **Acessar no navegador:**
   ```
   http://localhost:5173
   ```

## 🔧 Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Gera build de produção
- `npm run preview` - Visualiza o build de produção
- `npm run lint` - Executa o linter

## 🎨 Design System

### Cores Principais
- **Primary Blue**: `#3b82f6` (blue-600)
- **Secondary Gray**: `#64748b` (slate-500)
- **Background**: `#f8fafc` (slate-50)

### Tipografia
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

## 📱 Responsividade

O projeto foi desenvolvido com abordagem mobile-first e é totalmente responsivo:
- **Mobile**: 320px+
- **Tablet**: 768px+
- **Desktop**: 1024px+

## 🔮 Próximos Passos

- [ ] Integração com backend da Axiom API
- [ ] Implementação de autenticação real
- [ ] Dashboard do usuário
- [ ] Gerenciamento de instâncias WhatsApp
- [ ] Histórico de mensagens
- [ ] Configurações da conta

## 📄 Licença

Este projeto faz parte da plataforma Axiom API.
