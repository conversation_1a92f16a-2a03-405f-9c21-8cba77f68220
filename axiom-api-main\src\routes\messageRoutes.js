const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');

// Rota para enviar texto
router.post('/send-text', messageController.sendTextMessage);

// Rota para enviar imagem
router.post('/send-image', messageController.sendImageMessage);

// Rota para enviar áudio
router.post('/send-audio', messageController.sendAudioMessage);

// Rota para enviar vídeo
router.post('/send-video', messageController.sendVideoMessage);

// Rota para enviar documento
router.post('/send-document', messageController.sendDocumentMessage);



// Define a rota para verificar o status da conexão.
// GET /api/v1/status
router.get('/status', messageController.getConnectionStatus);

// Define a rota para obter o QR Code de autenticação.
// GET /api/v1/qr
router.get('/qr', messageController.getQRCode);

module.exports = router;
