const { Pool } = require('pg');

// Configuração do banco PostgreSQL
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'axiom_api',
    password: process.env.DB_PASSWORD || 'postgres',
    port: process.env.DB_PORT || 5432,
});

// Banco em memória para desenvolvimento (fallback)
let memoryDB = {
    users: [],
    instances: []
};

let useMemoryDB = false;

// Função para testar conexão
const testConnection = async () => {
    try {
        const client = await pool.connect();
        console.log('[DATABASE] Conexão com PostgreSQL estabelecida com sucesso');
        client.release();
        useMemoryDB = false;
    } catch (error) {
        console.warn('[DATABASE] PostgreSQL não disponível, usando banco em memória:', error.message);
        useMemoryDB = true;
    }
};

// Função para executar queries (com fallback para memória)
const query = async (text, params = []) => {
    if (useMemoryDB) {
        return executeMemoryQuery(text, params);
    }

    const start = Date.now();
    try {
        const res = await pool.query(text, params);
        const duration = Date.now() - start;
        console.log('[DATABASE] Query executada', { text: text.substring(0, 50) + '...', duration, rows: res.rowCount });
        return res;
    } catch (error) {
        console.error('[DATABASE] Erro na query:', error.message);
        throw error;
    }
};

// Simulador de queries para banco em memória
const executeMemoryQuery = (text, params) => {
    const lowerText = text.toLowerCase().trim();

    // INSERT INTO users
    if (lowerText.includes('insert into users')) {
        const [name, email, whatsappNumber, password, role] = params;
        const newUser = {
            id: require('uuid').v4(),
            name,
            email,
            whatsapp_number: whatsappNumber,
            password,
            role: role || 'user',
            created_at: new Date().toISOString()
        };
        memoryDB.users.push(newUser);
        return { rows: [newUser], rowCount: 1 };
    }

    // SELECT FROM users WHERE email
    if (lowerText.includes('select') && lowerText.includes('users') && lowerText.includes('email')) {
        const email = params[0];
        const user = memoryDB.users.find(u => u.email === email);
        return { rows: user ? [user] : [], rowCount: user ? 1 : 0 };
    }

    // SELECT FROM users WHERE id
    if (lowerText.includes('select') && lowerText.includes('users') && lowerText.includes('id = $1')) {
        const id = params[0];
        const user = memoryDB.users.find(u => u.id === id);
        return { rows: user ? [user] : [], rowCount: user ? 1 : 0 };
    }

    // SELECT ALL users
    if (lowerText.includes('select') && lowerText.includes('users') && lowerText.includes('order by')) {
        return { rows: memoryDB.users, rowCount: memoryDB.users.length };
    }

    // INSERT INTO instances
    if (lowerText.includes('insert into instances')) {
        const [name, ownerId, instanceId, instanceToken, status] = params;
        const newInstance = {
            id: require('uuid').v4(),
            name,
            owner_id: ownerId,
            instance_id: instanceId,
            instance_token: instanceToken,
            status: status || 'CREATED',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        memoryDB.instances.push(newInstance);
        return { rows: [newInstance], rowCount: 1 };
    }

    // SELECT instances WHERE owner_id
    if (lowerText.includes('select') && lowerText.includes('instances') && lowerText.includes('owner_id')) {
        const ownerId = params[0];
        const instances = memoryDB.instances.filter(i => i.owner_id === ownerId);
        return { rows: instances, rowCount: instances.length };
    }

    // SELECT instance WHERE id AND owner_id
    if (lowerText.includes('select') && lowerText.includes('instances') && lowerText.includes('id = $1 and owner_id = $2')) {
        const [id, ownerId] = params;
        const instance = memoryDB.instances.find(i => i.id === id && i.owner_id === ownerId);
        return { rows: instance ? [instance] : [], rowCount: instance ? 1 : 0 };
    }

    // DELETE instance
    if (lowerText.includes('delete from instances')) {
        const id = params[0];
        const index = memoryDB.instances.findIndex(i => i.id === id);
        if (index !== -1) {
            memoryDB.instances.splice(index, 1);
            return { rowCount: 1 };
        }
        return { rowCount: 0 };
    }

    // Default: retorna vazio
    return { rows: [], rowCount: 0 };
};

// Função para inicializar tabelas
const initializeTables = async () => {
    if (useMemoryDB) {
        console.log('[DATABASE] Usando banco em memória - tabelas simuladas');
        return;
    }

    try {
        // Criar tabela users
        await query(`
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                whatsapp_number VARCHAR(50),
                password VARCHAR(255) NOT NULL,
                role VARCHAR(50) DEFAULT 'user',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            )
        `);

        // Criar tabela instances
        await query(`
            CREATE TABLE IF NOT EXISTS instances (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                instance_id VARCHAR(255) UNIQUE NOT NULL,
                instance_token VARCHAR(255) UNIQUE NOT NULL,
                status VARCHAR(50) DEFAULT 'CREATED',
                qr_code TEXT,
                webhook_url VARCHAR(500),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            )
        `);

        // Criar índices para performance
        await query(`
            CREATE INDEX IF NOT EXISTS idx_instances_owner_id ON instances(owner_id);
            CREATE INDEX IF NOT EXISTS idx_instances_status ON instances(status);
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        `);

        console.log('[DATABASE] Tabelas PostgreSQL inicializadas com sucesso');
    } catch (error) {
        console.error('[DATABASE] Erro ao inicializar tabelas:', error.message);
        throw error;
    }
};

module.exports = {
    pool,
    query,
    testConnection,
    initializeTables
};
