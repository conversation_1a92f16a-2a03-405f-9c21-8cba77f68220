import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

interface User {
  id: string;
  name: string;
  email: string;
  whatsappNumber?: string;
  role?: string;
  createdAt?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (token: string, userData: User) => void;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    console.log('[AuthContext] Inicializando...');

    // Verificar localStorage de forma simples
    const token = localStorage.getItem('axiom-token');
    const userStr = localStorage.getItem('axiom-user');

    if (token && userStr) {
      try {
        const userData = JSON.parse(userStr);
        console.log('[AuthContext] Dados encontrados, restaurando sessão');
        setIsAuthenticated(true);
        setUser(userData);
      } catch (error) {
        console.error('[AuthContext] Erro ao parsear dados:', error);
        localStorage.removeItem('axiom-token');
        localStorage.removeItem('axiom-user');
      }
    }

    setLoading(false);
  }, []);

  const login = (token: string, userData: User) => {
    console.log('[AuthContext] Fazendo login...');

    // Salvar no localStorage de forma simples
    localStorage.setItem('axiom-token', token);
    localStorage.setItem('axiom-user', JSON.stringify(userData));

    // Atualizar estado
    setIsAuthenticated(true);
    setUser(userData);

    console.log('[AuthContext] Login concluído');
  };

  const logout = () => {
    console.log('[AuthContext] Fazendo logout');

    // Limpar localStorage
    localStorage.removeItem('axiom-token');
    localStorage.removeItem('axiom-user');

    // Atualizar estado
    setIsAuthenticated(false);
    setUser(null);

    // Navegar para login
    navigate('/login');
  };

  const value: AuthContextType = {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
