import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import storage from '../utils/storage';

interface User {
  id: string;
  name: string;
  email: string;
  whatsappNumber?: string;
  role?: string;
  createdAt?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (token: string, userData: User) => void;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    console.log('[AuthContext] Inicializando contexto de autenticação...');

    // Usar as funções utilitárias para verificar autenticação
    const validation = storage.validateAuthData();
    console.log('[AuthContext] Validação inicial:', validation);

    if (validation.isValid) {
      const token = storage.getToken();
      const user = storage.getUser();

      if (token && user) {
        console.log('[AuthContext] Dados válidos encontrados, restaurando sessão');
        setIsAuthenticated(true);
        setUser(user);
      }
    } else {
      console.log('[AuthContext] Dados inválidos ou ausentes:', validation.issues);
      // Limpar dados inválidos
      storage.clearAuth();
    }

    setLoading(false);
    console.log('[AuthContext] Inicialização concluída');
  }, []);

  const login = (token: string, userData: User) => {
    console.log('[AuthContext] Iniciando processo de login:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      userData: {
        id: userData?.id,
        name: userData?.name,
        email: userData?.email
      }
    });

    if (!token || !userData) {
      console.error('[AuthContext] Token ou dados do usuário inválidos');
      return;
    }

    // Usar função utilitária para salvar dados
    const success = storage.setAuthData(token, userData);

    if (success) {
      // Atualizar estado apenas se salvamento foi bem-sucedido
      setIsAuthenticated(true);
      setUser(userData);
      console.log('[AuthContext] Login realizado com sucesso - estado atualizado');

      // Verificar novamente se os dados estão íntegros
      const validation = storage.validateAuthData();
      if (!validation.isValid) {
        console.error('[AuthContext] Dados salvos estão corrompidos:', validation.issues);
        logout();
      }
    } else {
      console.error('[AuthContext] Falha ao salvar dados de login');
    }
  };

  const logout = () => {
    console.log('[AuthContext] Fazendo logout');

    // Usar função utilitária para limpar dados
    storage.clearAuth();

    // Atualizar estado
    setIsAuthenticated(false);
    setUser(null);

    // Navegar para login
    navigate('/login');
    console.log('[AuthContext] Logout concluído');
  };

  const value: AuthContextType = {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
