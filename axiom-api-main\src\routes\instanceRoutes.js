const express = require('express');
const { authenticateToken } = require('../middleware/authMiddleware');
const {
    getInstances,
    createInstance,
    getInstance,
    getInstanceStatus,
    getInstanceQR,
    deleteInstance
} = require('../controllers/instanceController');

const router = express.Router();

// Todas as rotas de instâncias requerem autenticação
router.use(authenticateToken);

// GET /api/v1/instances - Listar todas as instâncias do usuário
router.get('/instances', getInstances);

// POST /api/v1/instances - Criar nova instância
router.post('/instances', createInstance);

// GET /api/v1/instances/:id - Buscar instância específica
router.get('/instances/:id', getInstance);

// GET /api/v1/instances/:id/status - Obter status da instância
router.get('/instances/:id/status', getInstanceStatus);

// GET /api/v1/instances/:id/qr - Obter QR Code da instância
router.get('/instances/:id/qr', getInstanceQR);

// DELETE /api/v1/instances/:id - Deletar instância
router.delete('/instances/:id', deleteInstance);

module.exports = router;
