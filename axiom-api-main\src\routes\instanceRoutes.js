const express = require('express');
const {
    authenticateToken,
    requireInstanceOwnership
} = require('../middleware/authMiddleware');
const {
    getInstances,
    createInstance,
    getInstance,
    updateInstance,
    getInstanceStatus,
    getInstanceQR,
    deleteInstance
} = require('../controllers/instanceController');

const router = express.Router();

// Todas as rotas de instâncias requerem autenticação
router.use(authenticateToken);

// GET /api/v1/instances - Listar todas as instâncias do usuário
router.get('/instances', getInstances);

// POST /api/v1/instances - Criar nova instância
router.post('/instances', createInstance);

// GET /api/v1/instances/:id - Buscar instância específica
router.get('/instances/:id', requireInstanceOwnership, getInstance);

// PUT /api/v1/instances/:id - Atualizar instância
router.put('/instances/:id', requireInstanceOwnership, updateInstance);

// GET /api/v1/instances/:id/status - Obter status da instância
router.get('/instances/:id/status', requireInstanceOwnership, getInstanceStatus);

// GET /api/v1/instances/:id/qr - Obter QR Code da instância
router.get('/instances/:id/qr', requireInstanceOwnership, getInstanceQR);

// DELETE /api/v1/instances/:id - Deletar instância
router.delete('/instances/:id', requireInstanceOwnership, deleteInstance);

module.exports = router;
